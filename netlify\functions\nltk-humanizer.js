// netlify/functions/nltk-humanizer.js
// Self-contained NLTK-inspired humanization for Netlify
// Enhanced to match falconService.js quality
// No external dependencies, pure JavaScript implementation

/**
 * Comprehensive synonym database organized by POS tags (matching falconService.js)
 */
const SYNONYM_DATABASE = {
    // Adjectives (JJ)
    'JJ': {
        'good': ['excellent', 'great', 'fine', 'wonderful', 'superb', 'outstanding', 'remarkable', 'fantastic', 'terrific', 'awesome'],
        'bad': ['terrible', 'awful', 'horrible', 'dreadful', 'poor', 'lousy', 'rotten', 'nasty', 'unpleasant', 'disappointing'],
        'big': ['large', 'huge', 'enormous', 'massive', 'gigantic', 'vast', 'immense', 'colossal', 'tremendous', 'substantial'],
        'small': ['tiny', 'little', 'minute', 'compact', 'petite', 'miniature', 'microscopic', 'diminutive', 'modest', 'limited'],
        'important': ['crucial', 'vital', 'essential', 'significant', 'critical', 'key', 'major', 'fundamental', 'primary', 'central'],
        'different': ['distinct', 'unique', 'separate', 'various', 'diverse', 'alternative', 'contrasting', 'dissimilar', 'varied', 'other'],
        'new': ['fresh', 'recent', 'modern', 'latest', 'current', 'novel', 'innovative', 'contemporary', 'updated', 'brand-new'],
        'old': ['ancient', 'aged', 'elderly', 'vintage', 'antique', 'mature', 'seasoned', 'traditional', 'classic', 'outdated'],
        'high': ['elevated', 'tall', 'lofty', 'towering', 'superior', 'advanced', 'intense', 'extreme', 'peak', 'maximum'],
        'low': ['reduced', 'minimal', 'decreased', 'inferior', 'bottom', 'shallow', 'minor', 'slight', 'modest', 'limited'],
        'easy': ['simple', 'effortless', 'straightforward', 'uncomplicated', 'basic', 'elementary', 'smooth', 'manageable', 'clear', 'plain'],
        'hard': ['difficult', 'challenging', 'tough', 'complex', 'complicated', 'demanding', 'strenuous', 'arduous', 'rigorous', 'intense'],
        'fast': ['quick', 'rapid', 'swift', 'speedy', 'hasty', 'brisk', 'prompt', 'immediate', 'instant', 'accelerated'],
        'slow': ['gradual', 'leisurely', 'sluggish', 'delayed', 'unhurried', 'steady', 'measured', 'deliberate', 'prolonged', 'extended'],
        'significant': ['major', 'important', 'substantial', 'considerable', 'notable', 'meaningful', 'relevant', 'crucial', 'vital', 'key'],
        'comprehensive': ['complete', 'thorough', 'extensive', 'detailed', 'full', 'total', 'exhaustive', 'all-inclusive', 'wide-ranging', 'broad'],
        'effective': ['successful', 'efficient', 'productive', 'useful', 'powerful', 'potent', 'capable', 'competent', 'skillful', 'proficient'],
        'advanced': ['sophisticated', 'complex', 'developed', 'progressive', 'cutting-edge', 'state-of-the-art', 'modern', 'innovative', 'high-tech', 'evolved']
    },

    // Adverbs (RB)
    'RB': {
        'very': ['extremely', 'incredibly', 'remarkably', 'exceptionally', 'particularly', 'especially', 'quite', 'rather', 'pretty', 'fairly'],
        'really': ['truly', 'genuinely', 'actually', 'honestly', 'seriously', 'definitely', 'certainly', 'absolutely', 'completely', 'totally'],
        'quickly': ['rapidly', 'swiftly', 'speedily', 'hastily', 'promptly', 'immediately', 'instantly', 'briskly', 'efficiently', 'urgently'],
        'slowly': ['gradually', 'leisurely', 'steadily', 'carefully', 'deliberately', 'methodically', 'patiently', 'gently', 'cautiously', 'unhurriedly'],
        'often': ['frequently', 'regularly', 'commonly', 'typically', 'usually', 'generally', 'repeatedly', 'consistently', 'habitually', 'routinely'],
        'sometimes': ['occasionally', 'periodically', 'intermittently', 'sporadically', 'now and then', 'from time to time', 'once in a while', 'at times', 'every so often', 'infrequently'],
        'always': ['constantly', 'continuously', 'perpetually', 'consistently', 'invariably', 'forever', 'eternally', 'endlessly', 'permanently', 'unfailingly'],
        'never': ['not ever', 'at no time', 'under no circumstances', 'not once', 'not at all', 'by no means', 'absolutely not', 'certainly not', 'definitely not', 'positively not'],
        'clearly': ['obviously', 'evidently', 'apparently', 'plainly', 'distinctly', 'unmistakably', 'undoubtedly', 'certainly', 'definitely', 'undeniably'],
        'probably': ['likely', 'presumably', 'possibly', 'conceivably', 'potentially', 'maybe', 'perhaps', 'chances are', 'in all likelihood', 'most likely'],
        'furthermore': ['and', 'plus', 'also', 'on top of that', 'what\'s more', 'besides', 'additionally', 'moreover', 'in addition', 'as well'],
        'moreover': ['and', 'plus', 'also', 'what\'s more', 'besides', 'furthermore', 'additionally', 'in addition', 'as well', 'too'],
        'additionally': ['and', 'plus', 'also', 'on top of that', 'too', 'as well', 'furthermore', 'moreover', 'besides', 'in addition'],
        'consequently': ['so', 'which means', 'that\'s why', 'as a result', 'therefore', 'thus', 'hence', 'accordingly', 'for this reason', 'because of this'],
        'therefore': ['so', 'which means', 'that\'s why', 'hence', 'thus', 'consequently', 'as a result', 'for this reason', 'accordingly', 'because of this'],
        'thus': ['so', 'which means', 'that way', 'like this', 'in this way', 'therefore', 'hence', 'consequently', 'as a result', 'accordingly'],
        'hence': ['so', 'which is why', 'that\'s how', 'that\'s why', 'thus', 'therefore', 'consequently', 'as a result', 'for this reason', 'accordingly'],
        'subsequently': ['then', 'after that', 'next', 'later', 'afterwards', 'following that', 'in the aftermath', 'eventually', 'ultimately', 'finally'],
        'nevertheless': ['but', 'still', 'even so', 'however', 'yet', 'nonetheless', 'regardless', 'despite that', 'all the same', 'in spite of that'],
        'however': ['but', 'though', 'still', 'yet', 'even so', 'nevertheless', 'nonetheless', 'on the other hand', 'conversely', 'in contrast']
    },

    // Verbs (VB)
    'VB': {
        'show': ['demonstrate', 'display', 'exhibit', 'reveal', 'present', 'indicate', 'illustrate', 'manifest', 'expose', 'prove'],
        'make': ['create', 'produce', 'generate', 'construct', 'build', 'manufacture', 'form', 'develop', 'establish', 'cause'],
        'get': ['obtain', 'acquire', 'receive', 'gain', 'secure', 'achieve', 'attain', 'procure', 'fetch', 'retrieve'],
        'give': ['provide', 'offer', 'supply', 'deliver', 'present', 'grant', 'bestow', 'contribute', 'donate', 'hand over'],
        'use': ['utilize', 'employ', 'apply', 'implement', 'operate', 'handle', 'manipulate', 'exercise', 'practice', 'exploit'],
        'find': ['discover', 'locate', 'identify', 'detect', 'uncover', 'spot', 'encounter', 'come across', 'stumble upon', 'determine'],
        'think': ['believe', 'consider', 'suppose', 'assume', 'imagine', 'reckon', 'feel', 'suspect', 'presume', 'contemplate'],
        'know': ['understand', 'realize', 'recognize', 'comprehend', 'grasp', 'perceive', 'acknowledge', 'be aware of', 'be familiar with', 'appreciate'],
        'help': ['assist', 'aid', 'support', 'facilitate', 'contribute', 'serve', 'benefit', 'back', 'cooperate', 'collaborate'],
        'work': ['function', 'operate', 'perform', 'labor', 'toil', 'strive', 'endeavor', 'effort', 'serve', 'act'],
        'utilize': ['use', 'employ', 'apply', 'implement', 'operate', 'handle', 'manipulate', 'exercise', 'practice', 'exploit'],
        'implement': ['execute', 'carry out', 'put into practice', 'apply', 'perform', 'conduct', 'realize', 'accomplish', 'fulfill', 'enact'],
        'facilitate': ['help', 'assist', 'aid', 'support', 'enable', 'promote', 'encourage', 'foster', 'advance', 'expedite'],
        'demonstrate': ['show', 'display', 'exhibit', 'reveal', 'present', 'indicate', 'illustrate', 'manifest', 'prove', 'establish'],
        'establish': ['set up', 'create', 'found', 'institute', 'form', 'build', 'construct', 'develop', 'organize', 'launch'],
        'maintain': ['keep', 'preserve', 'sustain', 'continue', 'uphold', 'retain', 'support', 'conserve', 'protect', 'defend'],
        'generate': ['create', 'produce', 'make', 'cause', 'bring about', 'give rise to', 'spawn', 'engender', 'originate', 'develop'],
        'analyze': ['examine', 'study', 'investigate', 'scrutinize', 'evaluate', 'assess', 'review', 'inspect', 'dissect', 'break down'],
        'evaluate': ['assess', 'judge', 'appraise', 'review', 'examine', 'analyze', 'estimate', 'rate', 'measure', 'weigh'],
        'optimize': ['improve', 'enhance', 'refine', 'perfect', 'fine-tune', 'streamline', 'maximize', 'upgrade', 'boost', 'polish']
    },

    // Nouns (NN)
    'NN': {
        'thing': ['item', 'object', 'element', 'matter', 'subject', 'issue', 'aspect', 'factor', 'component', 'entity'],
        'way': ['method', 'approach', 'manner', 'technique', 'strategy', 'procedure', 'process', 'system', 'means', 'route'],
        'time': ['period', 'moment', 'instance', 'occasion', 'duration', 'interval', 'phase', 'stage', 'era', 'epoch'],
        'person': ['individual', 'human', 'being', 'character', 'figure', 'someone', 'citizen', 'resident', 'inhabitant', 'soul'],
        'place': ['location', 'spot', 'site', 'position', 'area', 'region', 'zone', 'venue', 'destination', 'setting'],
        'problem': ['issue', 'difficulty', 'challenge', 'obstacle', 'complication', 'dilemma', 'trouble', 'concern', 'matter', 'situation'],
        'idea': ['concept', 'notion', 'thought', 'suggestion', 'proposal', 'plan', 'scheme', 'theory', 'hypothesis', 'perspective'],
        'system': ['structure', 'framework', 'organization', 'arrangement', 'setup', 'network', 'mechanism', 'process', 'method', 'approach'],
        'information': ['data', 'details', 'facts', 'knowledge', 'intelligence', 'material', 'content', 'evidence', 'documentation', 'statistics'],
        'example': ['instance', 'case', 'illustration', 'sample', 'specimen', 'model', 'demonstration', 'representation', 'prototype', 'pattern'],
        'methodology': ['method', 'approach', 'technique', 'procedure', 'system', 'strategy', 'process', 'framework', 'protocol', 'way'],
        'implementation': ['execution', 'application', 'realization', 'deployment', 'installation', 'establishment', 'introduction', 'adoption', 'enactment', 'fulfillment'],
        'optimization': ['improvement', 'enhancement', 'refinement', 'perfection', 'upgrading', 'fine-tuning', 'streamlining', 'maximization', 'betterment', 'advancement'],
        'analysis': ['examination', 'study', 'investigation', 'review', 'assessment', 'evaluation', 'scrutiny', 'inspection', 'research', 'exploration'],
        'evaluation': ['assessment', 'appraisal', 'review', 'judgment', 'analysis', 'examination', 'estimation', 'rating', 'measurement', 'critique'],
        'framework': ['structure', 'system', 'foundation', 'platform', 'basis', 'infrastructure', 'architecture', 'skeleton', 'outline', 'scheme'],
        'paradigm': ['model', 'framework', 'pattern', 'template', 'example', 'standard', 'archetype', 'prototype', 'concept', 'approach'],
        'infrastructure': ['foundation', 'base', 'structure', 'framework', 'system', 'network', 'backbone', 'support', 'platform', 'architecture']
    }
};

/**
 * Determine POS tag for a word using pattern matching (matching falconService.js)
 */
function determinePOSTag(word) {
    // Adjective patterns (JJ)
    if (word.match(/^(very|quite|rather|extremely|highly|incredibly|amazingly|particularly|especially|remarkably)$/)) return 'RB';
    if (word.match(/(ful|less|ous|ive|able|ible|al|ic|ed|ing)$/)) return 'JJ';
    if (word.match(/^(good|bad|great|small|large|big|new|old|high|low|long|short|important|different|possible|available|necessary|special|certain|clear|simple|easy|difficult|hard|strong|weak|beautiful|ugly|happy|sad|young|early|late|recent|current|future|past|present|real|true|false|right|wrong|correct|proper|main|major|minor|basic|general|specific|particular|common|rare|unique|similar|different|various|several|many|few|much|little|more|most|less|least|best|worst|better|worse|first|last|next|previous|final|initial|original|natural|artificial|public|private|personal|professional|social|political|economic|financial|legal|medical|technical|scientific|educational|cultural|historical|modern|traditional|contemporary|ancient|recent|future|international|national|local|regional|global|worldwide|domestic|foreign|external|internal|upper|lower|middle|central|northern|southern|eastern|western|left|right|front|back|top|bottom|inside|outside|above|below|near|far|close|distant|direct|indirect|positive|negative|active|passive|open|closed|free|busy|full|empty|complete|incomplete|perfect|imperfect|normal|abnormal|regular|irregular|standard|special|ordinary|extraordinary|typical|unusual|common|rare|popular|unpopular|famous|unknown|successful|unsuccessful|effective|ineffective|efficient|inefficient|useful|useless|helpful|harmful|safe|dangerous|secure|insecure|stable|unstable|reliable|unreliable|accurate|inaccurate|exact|approximate|precise|vague|clear|unclear|obvious|hidden|visible|invisible|bright|dark|light|heavy|soft|hard|smooth|rough|hot|cold|warm|cool|wet|dry|clean|dirty|fresh|old|sharp|dull|loud|quiet|fast|slow|quick|gradual|sudden|immediate|delayed|temporary|permanent|brief|long|short|tall|wide|narrow|thick|thin|deep|shallow|rich|poor|expensive|cheap|valuable|worthless)$/)) return 'JJ';

    // Adverb patterns (RB)
    if (word.match(/(ly|ward|wise)$/)) return 'RB';
    if (word.match(/^(very|quite|rather|really|actually|basically|generally|usually|normally|typically|commonly|frequently|often|sometimes|occasionally|rarely|seldom|never|always|constantly|continuously|regularly|irregularly|immediately|instantly|quickly|slowly|gradually|suddenly|recently|currently|previously|formerly|originally|initially|finally|eventually|ultimately|definitely|certainly|probably|possibly|maybe|perhaps|obviously|clearly|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|absolutely|completely|totally|entirely|fully|partly|partially|mostly|mainly|primarily|chiefly|largely|generally|specifically|particularly|especially|notably|remarkably|significantly|considerably|substantially|slightly|somewhat|fairly|pretty|quite|rather|extremely|highly|incredibly|amazingly|surprisingly|interestingly|fortunately|unfortunately|hopefully|thankfully|regrettably|sadly|happily|luckily|unluckily|naturally|obviously|clearly|evidently|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|certainly|definitely|probably|possibly|maybe|perhaps|here|there|everywhere|anywhere|somewhere|nowhere|inside|outside|above|below|beneath|under|over|through|across|around|along|beside|behind|before|after|during|within|without|beyond|towards|away|forward|backward|upward|downward|inward|outward|homeward|eastward|westward|northward|southward|today|tomorrow|yesterday|now|then|soon|later|earlier|before|after|already|still|yet|again|once|twice|often|always|never|sometimes|usually|normally|typically|generally|specifically|particularly|especially|mainly|mostly|largely|primarily|chiefly|basically|essentially|fundamentally|originally|initially|finally|eventually|ultimately|immediately|instantly|quickly|slowly|gradually|suddenly|recently|currently|previously|formerly|definitely|certainly|probably|possibly|maybe|perhaps|obviously|clearly|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|absolutely|completely|totally|entirely|fully|partly|partially|slightly|somewhat|fairly|pretty|quite|rather|extremely|highly|incredibly|amazingly|surprisingly|interestingly|fortunately|unfortunately|hopefully|thankfully|regrettably|sadly|happily|luckily|unluckily|naturally)$/)) return 'RB';

    // Verb patterns (VB)
    if (word.match(/(ed|ing|s)$/)) return 'VB';
    if (word.match(/^(is|are|was|were|be|been|being|have|has|had|having|do|does|did|doing|will|would|could|should|might|may|can|must|shall|ought|need|dare|used)$/)) return 'VB';

    // Noun patterns (NN) - default
    return 'NN';
}

/**
 * Simple POS tagging for JavaScript (inspired by NLTK approach)
 */
function getPOSTags(sentence) {
    const words = sentence.match(/\w+/g) || [];
    const tags = [];

    for (const word of words) {
        const pos = determinePOSTag(word.toLowerCase());
        tags.push({ word, pos });
    }

    return tags;
}

/**
 * Get synonyms based on POS tag (inspired by NLTK WordNet approach)
 */
function getSynonymsByPOS(word, posTag) {
    // Get POS category
    let posCategory = 'NN'; // Default to noun
    if (posTag.startsWith('JJ')) posCategory = 'JJ';
    else if (posTag.startsWith('RB')) posCategory = 'RB';
    else if (posTag.startsWith('VB')) posCategory = 'VB';

    // Look up synonyms
    const categoryDatabase = SYNONYM_DATABASE[posCategory] || {};
    return categoryDatabase[word] || [];
}

/**
 * Select best synonym based on frequency and context
 */
function selectBestSynonym(synonyms, originalWord) {
    if (synonyms.length === 0) return originalWord;

    // For now, select randomly from first 3 synonyms (most common)
    const topSynonyms = synonyms.slice(0, Math.min(3, synonyms.length));
    return topSynonyms[Math.floor(Math.random() * topSynonyms.length)];
}

/**
 * Preserve original capitalization pattern
 */
function preserveCapitalization(original, replacement) {
    if (!original || !replacement) return replacement;

    // All uppercase
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }

    // First letter uppercase
    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }

    // All lowercase
    return replacement.toLowerCase();
}

/**
 * Replace word based on POS tag (inspired by NLTK approach)
 */
function replaceWordWithPOS(word, posTag, aggressiveness) {
    // Skip very short words or common words
    if (word.length <= 2 || isCommonWord(word)) {
        return word;
    }

    // Determine replacement probability based on POS tag
    let replacementProbability = 0.3; // Base probability

    if (posTag.startsWith('JJ')) {
        // Adjectives - higher replacement rate
        replacementProbability = 0.6 * aggressiveness;
    } else if (posTag.startsWith('RB')) {
        // Adverbs - higher replacement rate
        replacementProbability = 0.5 * aggressiveness;
    } else if (posTag.startsWith('VB')) {
        // Verbs - moderate replacement rate
        replacementProbability = 0.4 * aggressiveness;
    } else {
        // Nouns and others - lower replacement rate
        replacementProbability = 0.2 * aggressiveness;
    }

    // Random decision to replace
    if (Math.random() > replacementProbability) {
        return word;
    }

    // Get synonyms based on POS tag
    const synonyms = getSynonymsByPOS(word.toLowerCase(), posTag);

    if (synonyms.length === 0) {
        return word;
    }

    // Choose most appropriate synonym (frequency-based selection)
    const chosenSynonym = selectBestSynonym(synonyms, word);

    // Preserve original capitalization
    return preserveCapitalization(word, chosenSynonym);
}

/**
 * Check if word is too common to replace
 */
function isCommonWord(word) {
    const commonWords = new Set([
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
        'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
        'between', 'among', 'under', 'over', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
        'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
        'might', 'must', 'can', 'shall', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
        'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
        'her', 'its', 'our', 'their'
    ]);

    return commonWords.has(word.toLowerCase());
}

/**
 * Apply contractions to make text more casual
 */
function applyContractions(text) {
    const contractions = {
        'do not': 'don\'t',
        'will not': 'won\'t',
        'cannot': 'can\'t',
        'should not': 'shouldn\'t',
        'would not': 'wouldn\'t',
        'could not': 'couldn\'t',
        'it is': 'it\'s',
        'that is': 'that\'s',
        'there is': 'there\'s',
        'we are': 'we\'re',
        'they are': 'they\'re',
        'you are': 'you\'re',
        'I am': 'I\'m',
        'he is': 'he\'s',
        'she is': 'she\'s',
        'we have': 'we\'ve',
        'they have': 'they\'ve',
        'I have': 'I\'ve',
        'you have': 'you\'ve'
    };
    
    let result = text;
    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        result = result.replace(regex, contracted);
    });
    
    return result;
}

/**
 * Add casual qualifiers to make text more human-like
 */
function addCasualQualifiers(text, frequency = 0.15) {
    const qualifiers = [
        'pretty much', 'basically', 'sort of', 'kind of', 'more or less',
        'I think', 'probably', 'maybe', 'I guess', 'seems like'
    ];
    
    return text.replace(/\b(is|are|will|can|should|would|could)\b/g, (match) => {
        if (Math.random() < frequency) {
            const qualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)];
            return `${qualifier} ${match}`;
        }
        return match;
    });
}

/**
 * Clean symbols and spacing (similar to Python clean_symbols function)
 */
function cleanSymbols(text) {
    // Remove extra spaces
    let cleaned = text.replace(/\s+/g, ' ');

    // Fix spacing around punctuation - be more careful to preserve word spacing
    cleaned = cleaned.replace(/\s+([.!?,:;])/g, '$1'); // Remove space before punctuation
    cleaned = cleaned.replace(/([.!?])\s*([A-Z])/g, '$1 $2'); // Ensure space after sentence endings
    cleaned = cleaned.replace(/([.!?])([A-Z])/g, '$1 $2'); // Fix missing space after sentence endings
    cleaned = cleaned.replace(/,([A-Za-z])/g, ', $1'); // Fix missing space after commas

    return cleaned.trim();
}

/**
 * Main NLTK-inspired processing function (matching falconService.js)
 */
function processTextWithNLTKApproach(text, options = {}) {
    const { aggressiveness = 0.7, maintainTone = true, useAdvancedSynonyms = true } = options;

    // Preserve newlines with placeholder (similar to Python approach)
    const newlinePlaceholder = "庄周"; // Using same placeholder as Python code
    const textWithPlaceholders = text.replace(/\n/g, newlinePlaceholder);

    // Split into sentences while preserving symbols - improved regex
    const sentences = textWithPlaceholders.match(/[^.!?]*[.!?]+\s*|[^.!?]+$/g) || [textWithPlaceholders];

    const humanizedSentences = [];

    for (const sentence of sentences) {
        if (!sentence.trim()) {
            humanizedSentences.push(sentence);
            continue;
        }

        // Tokenize sentence into words and symbols
        const tokens = sentence.match(/\w+|[^\w\s]+/g) || [];

        // Get POS tags for the sentence
        const posTags = getPOSTags(sentence);

        // Process each token with POS-aware synonym replacement
        const humanizedTokens = [];
        let wordIndex = 0;

        for (const token of tokens) {
            if (/^\w+$/.test(token)) {
                // It's a word - apply POS-aware replacement
                const posTag = posTags[wordIndex] || { word: token, pos: 'NN' };
                const humanizedWord = replaceWordWithPOS(token, posTag.pos, aggressiveness);
                humanizedTokens.push(humanizedWord);
                wordIndex++;
            } else {
                // It's punctuation or symbol - keep as is
                humanizedTokens.push(token);
            }
        }

        // Reconstruct sentence with proper spacing
        let humanizedSentence = '';
        for (let i = 0; i < humanizedTokens.length; i++) {
            const token = humanizedTokens[i];

            // Add space before word tokens (except first token)
            if (i > 0 && /^\w/.test(token) && /\w$/.test(humanizedTokens[i-1])) {
                humanizedSentence += ' ';
            }

            humanizedSentence += token;
        }

        // Clean up spacing around symbols
        humanizedSentence = cleanSymbols(humanizedSentence);

        humanizedSentences.push(humanizedSentence);
    }

    // Join sentences and restore newlines
    let result = humanizedSentences.join('');

    // Final cleanup of spacing issues
    result = cleanSymbols(result);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Basic humanization with simple transformations
 */
function applyBasicHumanization(text, options = {}) {
    const { aggressiveness = 0.7 } = options;
    
    let result = text;
    
    // Apply contractions
    result = applyContractions(result);
    
    // Add casual qualifiers
    if (aggressiveness > 0.5) {
        result = addCasualQualifiers(result, aggressiveness * 0.15);
    }
    
    // Simple synonym replacement for common AI triggers
    const simpleReplacements = {
        'furthermore': 'and',
        'moreover': 'plus',
        'additionally': 'also',
        'consequently': 'so',
        'therefore': 'so',
        'utilize': 'use',
        'implement': 'do',
        'facilitate': 'help'
    };
    
    Object.entries(simpleReplacements).forEach(([formal, casual]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        result = result.replace(regex, casual);
    });
    
    return result;
}

module.exports = {
    processTextWithNLTKApproach,
    applyBasicHumanization
};
