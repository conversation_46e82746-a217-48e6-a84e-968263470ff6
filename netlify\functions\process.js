// netlify/functions/process.js
// Enhanced text processing function for Netlify with local humanization methods
// Optimized to use the same high-quality algorithms as src/services without API dependencies

const { addControlledMistakes, changeStyle, simpleParaphrase } = require('../../src/utils/textModifiers');
const { checkWithGPTZero } = require('../../src/services/gptzeroClient');
const { balancedHumanization, qualityCheck } = require('../../src/utils/balancedHumanizer');
const { humanizeText } = require('../../src/services/humaneyesService');

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // Handle preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    try {
        const { text, styleProfile, styleStrength } = JSON.parse(event.body);

        if (!text || typeof text !== 'string' || !text.trim()) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Input text is required and must be a non-empty string.' }),
            };
        }

        // Validate style parameters if provided
        if (styleStrength !== undefined && (typeof styleStrength !== 'number' || styleStrength < 0 || styleStrength > 100)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({ message: 'Style strength must be a number between 0 and 100.' }),
            };
        }

        let modifiedText = text; // Start with the original text

        const startTime = Date.now();

        // --- Step 1: Enhanced Humanization with Local Methods (No API Keys Required) ---
        console.log("Starting enhanced humanization with local methods optimized for Netlify...");

        try {
            // Calculate target aggressiveness based on style strength
            const baseAggressiveness = 0.7;
            const adjustedAggressiveness = styleStrength ?
                Math.min(0.95, baseAggressiveness + (styleStrength / 100) * 0.3) :
                baseAggressiveness;

            // Force local methods for Netlify deployment (no API dependencies)
            const humanizationResult = await humanizeText(modifiedText, {
                aggressiveness: adjustedAggressiveness,
                maintainTone: true,
                targetDetection: 10, // Target ≤10% AI detection
                method: 'nltk', // Force NLTK-based local humanization for Netlify
                fallbackEnabled: true,
                commercialGrade: false // Use standard local processing
            });

            if (humanizationResult.success) {
                modifiedText = humanizationResult.text || humanizationResult.humanizedText;
                const processingTime = Date.now() - startTime;
                console.log(`✅ Successfully humanized with ${humanizationResult.actualMethod || 'nltk'} method in ${processingTime}ms`);
                console.log(`🔧 Using LOCAL PROCESSING (no API keys required) for Netlify deployment`);

                // Log model information for debugging
                if (humanizationResult.model) {
                    console.log(`📊 Model used: ${humanizationResult.model}${humanizationResult.provider ? ` (${humanizationResult.provider})` : ''}`);
                }
            } else {
                console.warn(`NLTK humanization failed: ${humanizationResult.error}. Using local fallback methods.`);

                // Try advanced local humanization as first fallback
                try {
                    console.log("Attempting advanced local humanization fallback...");
                    const advancedResult = await humanizeText(modifiedText, {
                        aggressiveness: adjustedAggressiveness,
                        maintainTone: true,
                        targetDetection: 15, // Slightly relaxed for fallback
                        method: 'pattern', // Use pattern-based local method
                        fallbackEnabled: false
                    });

                    if (advancedResult.success) {
                        modifiedText = advancedResult.text || advancedResult.humanizedText;
                        console.log("Advanced local humanization fallback completed.");
                    } else {
                        throw new Error("Advanced local fallback failed");
                    }
                } catch (fallbackError) {
                    console.warn("Advanced local fallback failed, using simple methods:", fallbackError.message);

                    // Final fallback to simple paraphrasing (no API required)
                    modifiedText = await simpleParaphrase(modifiedText);
                    console.log("Simple paraphrasing fallback completed.");
                }
            }
        } catch (error) {
            console.error('Humanization error:', error.message);

            // Final fallback to simple paraphrasing
            console.log("Applying simple paraphrasing as final fallback...");
            modifiedText = await simpleParaphrase(modifiedText);
        }

        // --- Step 2: Apply enhanced balanced humanization with optional style (Local Processing) ---
        console.log("Applying enhanced balanced humanization with local methods...");
        if (styleProfile && styleStrength > 0) {
            console.log(`Applying personal style: ${styleProfile.name} at ${styleStrength}% strength`);
            // The balancedHumanization function uses local advanced methods
            const result = await balancedHumanization(modifiedText, styleProfile, styleStrength, {
                useAdvanced: true,
                aggressiveness: adjustedAggressiveness,
                maintainTone: true,
                localOnly: true // Force local processing for Netlify
            });
            modifiedText = typeof result === 'string' ? result : await result;
        } else {
            modifiedText = await balancedHumanization(modifiedText, null, 0, {
                useAdvanced: true,
                aggressiveness: adjustedAggressiveness,
                maintainTone: true,
                localOnly: true // Force local processing for Netlify
            });
        }

        // --- Step 3: Quality check ---
        console.log("Performing quality check...");
        const qualityResult = qualityCheck(modifiedText);

        if (qualityResult.hasIssues) {
            console.log("Quality issues detected:", qualityResult.issues);
            // Apply minimal additional processing if needed
            modifiedText = addControlledMistakes(modifiedText);
        } else {
            console.log("Quality check passed");
        }

        // --- Step 4: Apply subtle style changes ---
        console.log("Applying subtle style changes...");
        modifiedText = changeStyle(modifiedText);

        // --- Step 5: AI Detection Check ---
        console.log("Performing AI detection check...");
        const detectionResult = await checkWithGPTZero(modifiedText);

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ modifiedText, detectionResult }),
        };

    } catch (error) {
        console.error("Error in /api/process:", error);
        const errorMessage = error.message || 'Error processing text.';
        
        return {
            statusCode: 500,
            headers,
            body: JSON.stringify({
                message: errorMessage,
                error: error.toString(),
                detectionResult: {
                    error: true,
                    status: "Server Error",
                    message: "Failed to process text due to an internal server error.",
                    score: null
                }
            }),
        };
    }
};
