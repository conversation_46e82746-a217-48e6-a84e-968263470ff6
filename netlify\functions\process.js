// netlify/functions/process.js
// Enhanced text processing function for Netlify with NLTK-first humanization
// Fixed for ES modules compatibility and Netlify deployment

// Import self-contained humanization modules (no external dependencies)
const { processTextWithNLTKApproach, applyBasicHumanization } = require('./nltk-humanizer');
const {
    addControlledMistakes,
    changeStyle,
    simpleParaphrase,
    qualityCheck,
    balancedHumanization
} = require('./text-transformations');

exports.handler = async (event, context) => {
    // Set CORS headers
    const headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Content-Type': 'application/json',
    };

    // <PERSON>le preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return {
            statusCode: 200,
            headers,
            body: '',
        };
    }

    // Only allow POST method
    if (event.httpMethod !== 'POST') {
        return {
            statusCode: 405,
            headers,
            body: JSON.stringify({ message: `Method ${event.httpMethod} Not Allowed` }),
        };
    }

    const startTime = Date.now();

    try {
        // Enhanced request validation and diagnostics
        console.log("🚀 Starting NLTK-first text processing for Netlify...");
        console.log(`📋 Request details: method=${event.httpMethod}, path=${event.path}`);

        let requestBody;
        try {
            requestBody = JSON.parse(event.body);
        } catch (parseError) {
            console.error("❌ Failed to parse request body:", parseError.message);
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    message: 'Invalid JSON in request body',
                    error: parseError.message
                }),
            };
        }

        const { text, styleProfile, styleStrength } = requestBody;

        // Enhanced input validation
        if (!text) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    message: 'Input text is required',
                    error: 'Missing text parameter'
                }),
            };
        }

        if (typeof text !== 'string') {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    message: 'Input text must be a string',
                    error: `Received ${typeof text} instead of string`
                }),
            };
        }

        if (!text.trim()) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    message: 'Input text cannot be empty or whitespace only',
                    error: 'Empty text after trimming'
                }),
            };
        }

        // Validate text length limits
        if (text.length > 50000) { // 50KB limit for Netlify functions
            return {
                statusCode: 413,
                headers,
                body: JSON.stringify({
                    success: false,
                    message: 'Input text too large',
                    error: `Text length ${text.length} exceeds maximum of 50,000 characters`
                }),
            };
        }

        // Validate style parameters if provided
        if (styleStrength !== undefined && (typeof styleStrength !== 'number' || styleStrength < 0 || styleStrength > 100)) {
            return {
                statusCode: 400,
                headers,
                body: JSON.stringify({
                    success: false,
                    message: 'Style strength must be a number between 0 and 100',
                    error: `Received styleStrength: ${styleStrength} (${typeof styleStrength})`
                }),
            };
        }

        console.log(`📝 Input validated: ${text.length} characters, styleStrength=${styleStrength}`);

        let modifiedText = text; // Start with the original text

        // --- Step 1: NLTK-First Humanization (Optimized for Netlify) ---
        console.log("🔬 Starting NLTK-first humanization (no external APIs)...");

        // Environment diagnostics
        console.log("🔍 Environment diagnostics:");
        console.log(`- NODE_ENV: ${process.env.NODE_ENV}`);
        console.log(`- NETLIFY: ${process.env.NETLIFY}`);
        console.log(`- Text length: ${modifiedText.length} characters`);

        // Calculate target aggressiveness based on style strength
        const baseAggressiveness = 0.7;
        const adjustedAggressiveness = styleStrength ?
            Math.min(0.95, baseAggressiveness + (styleStrength / 100) * 0.3) :
            baseAggressiveness;

        console.log(`📊 Processing parameters: aggressiveness=${adjustedAggressiveness}, styleStrength=${styleStrength}`);

        // Define humanization methods in order of preference (NLTK first, no external APIs)
        const humanizationMethods = [
            { name: 'nltk', description: 'NLTK-inspired approach (preferred for Netlify)' },
            { name: 'basic', description: 'Basic humanization transformations' },
            { name: 'simple', description: 'Simple paraphrasing (final fallback)' }
        ];

        let methodAttempts = [];

        // Try each method in order until one succeeds
        for (const method of humanizationMethods) {
            const methodStartTime = Date.now();
            console.log(`🚀 Attempting ${method.name} method: ${method.description}`);

            try {
                let result;

                if (method.name === 'nltk') {
                    // Use NLTK-inspired processing
                    result = processTextWithNLTKApproach(modifiedText, {
                        aggressiveness: adjustedAggressiveness,
                        maintainTone: true
                    });
                } else if (method.name === 'basic') {
                    // Use basic humanization
                    result = applyBasicHumanization(modifiedText, {
                        aggressiveness: adjustedAggressiveness
                    });
                } else if (method.name === 'simple') {
                    // Use simple paraphrasing
                    result = simpleParaphrase(modifiedText);
                }

                const methodTime = Date.now() - methodStartTime;

                if (result && result !== modifiedText) {
                    console.log(`✅ ${method.name} method succeeded in ${methodTime}ms`);
                    console.log(`📈 Result stats: ${modifiedText.length} → ${result.length} chars`);

                    const transformationRate = ((Math.abs(modifiedText.length - result.length) / modifiedText.length) * 100).toFixed(1);
                    console.log(`🔄 Transformation rate: ${transformationRate}%`);

                    methodAttempts.push({
                        method: method.name,
                        success: true,
                        time: methodTime,
                        transformationRate: transformationRate
                    });

                    modifiedText = result;
                    break; // Success! Exit the loop
                } else {
                    console.warn(`⚠️ ${method.name} method failed in ${methodTime}ms: No transformation applied`);

                    methodAttempts.push({
                        method: method.name,
                        success: false,
                        time: methodTime,
                        error: 'No transformation applied'
                    });
                }
            } catch (error) {
                const methodTime = Date.now() - methodStartTime;
                console.error(`❌ ${method.name} method threw exception in ${methodTime}ms:`, {
                    message: error.message,
                    name: error.name
                });

                methodAttempts.push({
                    method: method.name,
                    success: false,
                    time: methodTime,
                    error: error.message,
                    exception: true
                });
            }
        }

        // Log final method summary
        console.log("📊 Method attempt summary:", JSON.stringify(methodAttempts, null, 2));

        // --- Step 2: Apply enhanced balanced humanization with optional style ---
        console.log("🎨 Applying balanced humanization with style...");
        try {
            if (styleProfile && styleStrength > 0) {
                console.log(`Applying personal style: ${styleProfile.name} at ${styleStrength}% strength`);
                modifiedText = balancedHumanization(modifiedText, styleProfile, styleStrength, {
                    aggressiveness: adjustedAggressiveness,
                    maintainTone: true
                });
            } else {
                modifiedText = balancedHumanization(modifiedText, null, 0, {
                    aggressiveness: adjustedAggressiveness,
                    maintainTone: true
                });
            }
            console.log("✅ Balanced humanization completed");
        } catch (error) {
            console.warn("⚠️ Balanced humanization failed:", error.message);
            // Continue with current text
        }

        // --- Step 3: Quality check ---
        console.log("🔍 Performing quality check...");
        try {
            const qualityResult = qualityCheck(modifiedText);

            if (qualityResult.hasIssues) {
                console.log("⚠️ Quality issues detected:", qualityResult.issues);
                // Apply minimal additional processing if needed
                modifiedText = addControlledMistakes(modifiedText, 0.05);
                console.log("🔧 Applied controlled mistakes to address quality issues");
            } else {
                console.log("✅ Quality check passed");
            }
        } catch (error) {
            console.warn("⚠️ Quality check failed:", error.message);
            // Continue without quality check
        }

        // --- Step 4: Apply subtle style changes ---
        console.log("🎭 Applying subtle style changes...");
        try {
            modifiedText = changeStyle(modifiedText);
            console.log("✅ Style changes applied");
        } catch (error) {
            console.warn("⚠️ Style changes failed:", error.message);
            // Continue with current text
        }

        const totalProcessingTime = Date.now() - startTime;
        console.log(`🎉 Processing completed in ${totalProcessingTime}ms`);
        console.log(`📊 Final text length: ${modifiedText.length} characters`);

        // Enhanced response with debugging information (no external AI detection)
        const response = {
            success: true,
            modifiedText,
            detectionResult: {
                status: "Processing Complete",
                message: "Text humanized using NLTK-based approach (no external AI detection on Netlify)",
                score: null,
                method: "netlify-nltk"
            },
            processingStats: {
                totalTime: totalProcessingTime,
                originalLength: text.length,
                finalLength: modifiedText.length,
                methodAttempts: methodAttempts,
                environment: {
                    nodeEnv: process.env.NODE_ENV,
                    netlify: process.env.NETLIFY,
                    platform: 'netlify-functions'
                }
            }
        };

        return {
            statusCode: 200,
            headers,
            body: JSON.stringify(response),
        };

    } catch (error) {
        const totalProcessingTime = Date.now() - startTime;

        // Enhanced error logging with full context
        console.error("❌ Critical error in Netlify function:", {
            message: error.message,
            name: error.name,
            stack: error.stack,
            processingTime: totalProcessingTime,
            textLength: text?.length || 'unknown'
        });

        // Determine error type and appropriate response
        let statusCode = 500;
        let errorCategory = 'Internal Server Error';

        if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
            statusCode = 504;
            errorCategory = 'Gateway Timeout';
        } else if (error.message.includes('memory') || error.message.includes('Memory')) {
            statusCode = 507;
            errorCategory = 'Insufficient Storage';
        }

        const errorResponse = {
            success: false,
            message: `${errorCategory}: ${error.message}`,
            error: {
                type: error.name || 'UnknownError',
                message: error.message,
                category: errorCategory,
                processingTime: totalProcessingTime,
                timestamp: new Date().toISOString()
            },
            detectionResult: {
                error: true,
                status: errorCategory,
                message: "Failed to process text due to an internal server error.",
                score: null
            },
            // Include debugging info for troubleshooting
            debug: {
                environment: process.env.NODE_ENV,
                netlify: process.env.NETLIFY,
                platform: 'netlify-functions',
                textLength: text?.length || 'unknown'
            }
        };

        return {
            statusCode,
            headers,
            body: JSON.stringify(errorResponse),
        };
    }
};
