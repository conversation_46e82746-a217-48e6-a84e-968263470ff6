/**
 * Falcon/DeepSeek-R1 Humanization Service
 * High-performance text humanization using advanced language models
 * Replaces pattern-based approach with LLM-based humanization for ≤10% AI detection
 */

import axios from 'axios';
import { validateWithRealTimeDetection, isRealTimeDetectionAvailable } from './aiDetectionService.js';

// Model configurations for different providers - DeepSeek-R1 Primary with Falcon fallback
const MODEL_CONFIGS = {
    // Primary DeepSeek-R1 with DeepThink reasoning capabilities
    'deepseek-r1': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/deepseek-r1',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 1,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            },
            {
                name: 'novita',
                endpoint: 'https://api.novita.ai/v3/openai/chat/completions',
                model: 'deepseek-r1',
                apiKeyEnv: 'NOVITA_API_KEY',
                priority: 2,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            },
            {
                name: 'openrouter',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-r1',
                apiKeyEnv: 'OPENROUTER_API_KEY',
                priority: 3,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            }
        ]
    },
    // Secondary Falcon models for fallback
    'falcon-3-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 4
            },
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/falcon-3-7b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 5
            }
        ]
    },
    'falcon-h1-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon-H1-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon-H1-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-3-10b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-10B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-10B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-180b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/falcon-180B-chat/v1/chat/completions',
                model: 'tiiuae/falcon-180B-chat',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 3 // Lower priority due to cost
            }
        ]
    },

    'llama-3.1-8b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/llama-v3p1-8b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 6
            },
            {
                name: 'groq',
                endpoint: 'https://api.groq.com/openai/v1/chat/completions',
                model: 'llama-3.1-8b-instant',
                apiKeyEnv: 'GROQ_API_KEY',
                priority: 7
            }
        ]
    },
    'mistral-7b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/mistral-7b-instruct-4k',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 8
            }
        ]
    }
};

/**
 * ZeroGPT-specific detection patterns that must be eliminated
 */
const ZEROGPT_DETECTION_PATTERNS = {
    formalTransitions: ['furthermore', 'moreover', 'additionally', 'consequently', 'therefore', 'thus', 'hence', 'subsequently', 'nevertheless'],
    roboticQualifiers: ['it is important to note', 'it should be mentioned', 'it is worth noting', 'it must be emphasized', 'it is essential to understand'],
    passiveConstructions: ['is implemented', 'are utilized', 'can be achieved', 'has been developed', 'will be conducted', 'are being processed'],
    corporateJargon: ['leverage', 'optimize', 'facilitate', 'comprehensive', 'strategic', 'innovative', 'synergistic', 'paradigm', 'methodology'],
    academicFormality: ['this study', 'the analysis', 'the findings', 'research indicates', 'data suggests', 'results demonstrate'],
    mechanicalStructures: ['firstly', 'secondly', 'thirdly', 'finally', 'in conclusion', 'to summarize', 'in summary'],
    perfectGrammar: /^[A-Z][^.!?]*[.!?]$/g, // Perfect sentence structure
    consistentLength: /^.{50,80}$/g // Consistent sentence lengths
};

/**
 * Advanced AI pattern analysis for targeted destruction
 * Identifies critical patterns that must be eliminated for ≤10% detection
 */
function analyzeAIPatterns(text) {
    const patterns = [
        // Critical severity patterns (must be eliminated)
        { pattern: 'formal transitions', regex: /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|specifically|essentially|ultimately)\b/gi, severity: 'critical', weight: 4 },
        { pattern: 'robotic qualifiers', regex: /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized|it is essential to understand)\b/gi, severity: 'critical', weight: 5 },
        { pattern: 'mechanical conclusions', regex: /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly|overall|in essence)\b/gi, severity: 'critical', weight: 4 },
        { pattern: 'passive voice overuse', regex: /\b(is|are|was|were|been|being)\s+\w+ed\b/gi, severity: 'critical', weight: 3 },

        // High severity patterns (should be eliminated)
        { pattern: 'academic vocabulary', regex: /\b(utilize|implement|facilitate|optimize|demonstrate|establish|maintain|generate|analyze|evaluate)\b/gi, severity: 'high', weight: 3 },
        { pattern: 'over-qualification', regex: /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various|multiple|several|diverse)\b/gi, severity: 'high', weight: 2 },
        { pattern: 'corporate jargon', regex: /\b(leverage|strategize|synergize|optimize|streamline|maximize|enhance|facilitate)\b/gi, severity: 'high', weight: 3 },

        // Medium severity patterns (reduce frequency)
        { pattern: 'perfect grammar', regex: /^[A-Z][^.!?]*[.!?]$/gm, severity: 'medium', weight: 1 },
        { pattern: 'repetitive structure', regex: /^[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]/gm, severity: 'medium', weight: 2 }
    ];

    return patterns.map(({ pattern, regex, severity, weight }) => {
        const matches = text.match(regex) || [];
        return {
            pattern,
            count: matches.length,
            severity,
            weight,
            examples: matches.slice(0, 3),
            riskScore: matches.length * weight
        };
    }).filter(p => p.count > 0);
}

/**
 * Generate ultra-aggressive humanization prompt optimized for DeepSeek-R1 with enhanced targeting
 * Redesigned for consistent ≤10% AI detection achievement
 */
function generateHumanizationPrompt(text, options = {}) {
    const {
        aggressiveness = 0.8, // Increased default for better results
        targetDetection = 10,
        contentType = 'general',
        modelType = 'deepseek-r1',
        enableDeepThink = true,
        passNumber = 1,
        previousAttempts = 0
    } = options;

    // Enhanced aggressiveness calculation with exponential scaling for retries
    const baseAggressiveness = Math.min(aggressiveness + (previousAttempts * 0.15), 1.0);
    const aggressivenessLevel = baseAggressiveness > 0.95 ? 'NUCLEAR' :
                               baseAggressiveness > 0.85 ? 'MAXIMUM' :
                               baseAggressiveness > 0.75 ? 'ULTRA-HIGH' :
                               baseAggressiveness > 0.65 ? 'HIGH' : 'MODERATE';

    // Advanced AI pattern analysis for targeted destruction
    const aiPatternAnalysis = analyzeAIPatterns(text);
    const criticalPatterns = aiPatternAnalysis.filter(p => p.severity === 'critical');
    const highRiskPatterns = aiPatternAnalysis.filter(p => p.severity === 'high');
    const totalRiskScore = aiPatternAnalysis.reduce((sum, p) => sum + p.riskScore, 0);

    // Enhanced DeepSeek-R1 reasoning with pattern-specific targeting
    const deepThinkPrefix = enableDeepThink && modelType === 'deepseek-r1' ? `<think>
MISSION: NUCLEAR HUMANIZATION - TARGET: ≤${targetDetection}% AI DETECTION

INTELLIGENCE ANALYSIS:
- Pass Number: ${passNumber}
- Previous Attempts: ${previousAttempts}
- Aggressiveness Level: ${aggressivenessLevel} (${(baseAggressiveness * 100).toFixed(0)}%)
- Content Type: ${contentType}
- Total Risk Score: ${totalRiskScore}/100

CRITICAL AI PATTERNS DETECTED (MUST OBLITERATE):
${criticalPatterns.map(p => `- ${p.pattern.toUpperCase()}: ${p.count} instances (Risk: ${p.riskScore})`).join('\n')}

HIGH-RISK PATTERNS (ELIMINATE):
${highRiskPatterns.map(p => `- ${p.pattern}: ${p.count} instances (Risk: ${p.riskScore})`).join('\n')}

NUCLEAR TRANSFORMATION PROTOCOL:
1. ANNIHILATE formal transitions → casual connectors (furthermore→and, moreover→plus, consequently→so)
2. OBLITERATE robotic qualifiers → human expressions (it is important to note→here's the thing)
3. VAPORIZE academic vocabulary → everyday language (utilize→use, implement→do, facilitate→help)
4. INJECT MASSIVE contractions (minimum 85% conversion rate)
5. FLOOD with casual language markers (really, pretty, quite, honestly, actually, basically)
6. SHATTER long sentences → punchy fragments with natural flow
7. IMPLANT human imperfections and natural speech patterns
8. SCRAMBLE sentence structures → completely varied beginnings
9. EMBED conversational elements and personal touches
10. GUARANTEE ${baseAggressiveness > 0.85 ? '90%+' : '75%+'} word transformation rate

TRANSFORMATION INTENSITY: ${aggressivenessLevel}
MERCY LEVEL: ABSOLUTE ZERO
SUCCESS CRITERIA: Text must sound like a casual human conversation, not AI writing

${totalRiskScore > 50 ? 'HIGH ALERT: Extreme AI pattern density detected - applying NUCLEAR protocols' : 'STANDARD ALERT: Applying maximum humanization'}

EXECUTING NUCLEAR HUMANIZATION SEQUENCE...
</think>

` : '';

    // Enhanced prompt with pattern-specific instructions
    const patternInstructions = criticalPatterns.length > 0 ?
        `\n\n🚨 CRITICAL PATTERNS DETECTED - IMMEDIATE ELIMINATION REQUIRED:
${criticalPatterns.map(p => `- ${p.pattern.toUpperCase()}: ${p.count} instances → MUST BE COMPLETELY REPLACED`).join('\n')}

HIGH-PRIORITY TARGETS:
${highRiskPatterns.map(p => `- ${p.pattern}: ${p.count} instances → Replace with casual alternatives`).join('\n')}` : '';

    const intensityInstructions = baseAggressiveness > 0.9 ?
        '\n\n⚠️ NUCLEAR MODE ACTIVATED: Apply maximum transformation with zero mercy for formal language' :
        baseAggressiveness > 0.8 ?
        '\n\n🔥 MAXIMUM MODE: Extremely aggressive transformation required' :
        '\n\n💪 HIGH MODE: Strong transformation with casual language focus';

    const prompt = `${deepThinkPrefix}You are a NUCLEAR-LEVEL text humanization specialist. Your mission is to COMPLETELY OBLITERATE all AI detection patterns and achieve ≤${targetDetection}% detection on ZeroGPT, Originality.ai, and GPTZero.

🎯 TRANSFORMATION PARAMETERS:
- Detection Target: ≤${targetDetection}% (ZERO TOLERANCE for failure)
- Intensity Level: ${aggressivenessLevel} (${(baseAggressiveness * 100).toFixed(0)}% power)
- Content Type: ${contentType}
- Risk Score: ${totalRiskScore}/100 (${totalRiskScore > 50 ? 'EXTREME RISK' : 'HIGH RISK'})
- Word Change Requirement: MINIMUM ${baseAggressiveness > 0.85 ? '85%' : '75%'} transformation${patternInstructions}${intensityInstructions}
- Structure: Break formal structures - use natural human speech patterns

⚡ ULTRA-AGGRESSIVE EXECUTION COMMANDS:
1. OBLITERATE every formal word and replace with casual alternatives
2. DESTROY all passive voice - make everything active and personal
3. INJECT massive amounts of contractions, casual language, and personal opinions
4. BREAK every long sentence into shorter, punchier human-style sentences
5. ADD conversational elements that no AI would naturally use
6. ELIMINATE all corporate/academic jargon completely

💥 EXTREME HUMANIZATION ATTACK PROTOCOLS:

1. SENTENCE DEMOLITION & RECONSTRUCTION:
   - SMASH every formal sentence structure completely
   - REBUILD using casual, conversational patterns
   - START sentences with: "Look", "Listen", "Here's the deal", "You know what"
   - END with natural conclusions: "...and that's it", "...pretty simple really"
   - MIX short punchy statements with longer rambling explanations

2. VOCABULARY ANNIHILATION:
   - DESTROY formal words: "utilize" → "use", "implement" → "do", "facilitate" → "help"
   - ELIMINATE corporate speak: "leverage" → "use", "optimize" → "make better"
   - REPLACE academic terms: "analysis" → "looking at", "methodology" → "way of doing things"
   - INJECT slang and casual terms: "stuff", "things", "pretty much", "basically"

3. MASSIVE HUMAN IMPERFECTION INJECTION (20-30% frequency):
   - FLOOD with hesitation: "I think", "maybe", "probably", "I guess", "sort of"
   - ADD redundancy: "really really", "pretty much exactly", "kind of sort of"
   - INSERT uncertainty: "I'm not 100% sure but", "could be wrong but", "seems like"
   - INCLUDE self-correction: "well, actually", "or rather", "I mean"

4. CONVERSATIONAL CHAOS INJECTION:
   - BREAK formal grammar rules intentionally
   - USE fragments: "Which is great." "Really important stuff." "Makes sense."
   - ADD run-on sentences with multiple "and"s and "but"s
   - INSERT casual interruptions: "anyway", "by the way", "oh and"

5. PERSONAL OPINION BOMBARDMENT:
   - INJECT personal views: "I think", "in my opinion", "honestly", "to be frank"
   - ADD emotional reactions: "which is awesome", "that's pretty cool", "kind of annoying"
   - INCLUDE experience references: "I've seen this before", "from what I know"
   - USE direct address: "you know", "you see", "you get what I mean"

6. ANTI-AI PATTERN WARFARE:
   - ELIMINATE all transition words completely
   - DESTROY parallel structures and lists
   - REMOVE all passive voice constructions
   - OBLITERATE formal conclusions and summaries

🎯 ZEROGPT DETECTION ELIMINATION TARGETS (ZERO TOLERANCE):
- FORMAL TRANSITIONS: ${ZEROGPT_DETECTION_PATTERNS.formalTransitions.join(', ')}
- ROBOTIC QUALIFIERS: ${ZEROGPT_DETECTION_PATTERNS.roboticQualifiers.join(', ')}
- PASSIVE CONSTRUCTIONS: ${ZEROGPT_DETECTION_PATTERNS.passiveConstructions.join(', ')}
- CORPORATE JARGON: ${ZEROGPT_DETECTION_PATTERNS.corporateJargon.join(', ')}
- ACADEMIC FORMALITY: ${ZEROGPT_DETECTION_PATTERNS.academicFormality.join(', ')}
- MECHANICAL STRUCTURES: ${ZEROGPT_DETECTION_PATTERNS.mechanicalStructures.join(', ')}
- PERFECT GRAMMAR: No perfectly structured sentences allowed
- CONSISTENT LENGTHS: Vary sentence lengths dramatically
- TECHNICAL PRECISION: Add human uncertainty and casual language

💪 TRANSFORMATION EXAMPLES:
BEFORE: "The implementation of this solution requires comprehensive analysis."
AFTER: "Look, if you want to actually do this thing, you've got to really dig into it and figure out what's going on."

BEFORE: "Furthermore, it is important to note that optimization is essential."
AFTER: "And here's the thing - you absolutely have to make this stuff work better. No question about it."

BEFORE: "The analysis reveals significant improvements in performance metrics."
AFTER: "So when we looked at this, turns out it actually makes things run way better. Pretty cool stuff."

🚨 MANDATORY REQUIREMENTS:
- MINIMUM 70% of words must be completely different
- ZERO formal language allowed
- MAXIMUM casual, conversational tone
- ADD personal opinions and uncertainty
- BREAK all formal sentence structures
- INJECT contractions and casual speech patterns

📝 CONTENT TO HUMANIZE:
${text}

🎭 ULTRA-HUMANIZED OUTPUT (Target: ≤${targetDetection}% ZeroGPT Detection):`;

    return prompt;
}

/**
 * Enhanced API call function with Falcon-specific optimizations
 */
export async function callLLMAPI(provider, prompt, options = {}) {
    const {
        maxTokens = 4000,
        temperature = 0.7,
        topP = 0.9,
        timeout = 45000, // Increased timeout for reasoning models
        modelType = 'deepseek-r1',
        enableDeepThink = false,
        reasoningTokens = 1000
    } = options;

    const apiKey = process.env[provider.apiKeyEnv];
    if (!apiKey) {
        throw new Error(`API key not found for ${provider.name}: ${provider.apiKeyEnv}`);
    }

    // Enhanced parameter optimization for maximum humanization performance
    const enhancedParams = calculateOptimalParameters(
        options.aggressiveness || 0.8,
        modelType,
        options.targetDetection || 10,
        options.passNumber || 1
    );

    const optimizedParams = optimizeParametersForModel(provider.model, {
        temperature: enhancedParams.temperature,
        topP: enhancedParams.topP,
        maxTokens: Math.min(6000, Math.floor((options.textLength || 1000) * enhancedParams.maxTokensMultiplier)),
        modelType,
        targetDetection: options.targetDetection || 10,
        enableDeepThink,
        reasoningTokens: modelType === 'deepseek-r1' ? Math.min(2000, reasoningTokens * 1.5) : reasoningTokens,
        repetitionPenalty: enhancedParams.repetitionPenalty,
        scaledAggressiveness: enhancedParams.scaledAggressiveness
    });

    const requestData = {
        model: provider.model,
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        max_tokens: optimizedParams.maxTokens,
        temperature: optimizedParams.temperature,
        top_p: optimizedParams.topP,
        stream: false
    };

    // Add fine-tuned model-specific parameters for ≤10% AI detection
    if (modelType === 'deepseek-r1') {
        // DeepSeek-R1 specific parameters for DeepThink reasoning
        requestData.repetition_penalty = optimizedParams.repetitionPenalty || 1.05;
        requestData.presence_penalty = optimizedParams.presencePenalty || 0.45;
        requestData.frequency_penalty = optimizedParams.frequencyPenalty || 0.55;

        // DeepThink reasoning parameters
        if (optimizedParams.enableDeepThink) {
            requestData.reasoning_effort = 'high';
            requestData.max_reasoning_tokens = optimizedParams.reasoningTokens || 1000;
            console.log(`🧠 DeepThink enabled: ${optimizedParams.reasoningTokens} reasoning tokens`);
        }

        // Enhanced sampling for reasoning
        requestData.do_sample = true;
        requestData.top_k = 50;

    } else if (modelType === 'falcon') {
        requestData.repetition_penalty = optimizedParams.repetitionPenalty || 1.15;
        requestData.presence_penalty = optimizedParams.presencePenalty || 0.3;
        requestData.frequency_penalty = optimizedParams.frequencyPenalty || 0.4;
        requestData.do_sample = true;
        requestData.pad_token_id = 50256; // Ensure proper tokenization
    } else {
        // Apply optimized parameters for other models
        if (optimizedParams.repetitionPenalty) {
            requestData.repetition_penalty = optimizedParams.repetitionPenalty;
        }
        if (optimizedParams.presencePenalty) {
            requestData.presence_penalty = optimizedParams.presencePenalty;
        }
        if (optimizedParams.frequencyPenalty) {
            requestData.frequency_penalty = optimizedParams.frequencyPenalty;
        }
    }

    const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    };

    // Add provider-specific headers and configurations
    if (provider.name === 'openrouter') {
        headers['HTTP-Referer'] = process.env.NEXT_PUBLIC_APP_URL || 'https://ghostlayer.netlify.app';
        headers['X-Title'] = 'GhostLayer';
    } else if (provider.name === 'huggingface') {
        headers['X-Use-Cache'] = 'false'; // Ensure fresh responses
        headers['X-Wait-For-Model'] = 'true'; // Wait for model to load
    }

    try {
        console.log(`Calling ${provider.name} API with Falcon model ${provider.model}...`);

        const startTime = Date.now();
        const response = await axios.post(provider.endpoint, requestData, {
            headers,
            timeout
        });
        const responseTime = Date.now() - startTime;

        if (response.data && response.data.choices && response.data.choices[0]) {
            const fullResponse = response.data.choices[0].message.content.trim();

            // Extract reasoning chain and final output for DeepSeek-R1
            const reasoningValidation = validateReasoningChain(fullResponse, modelType, optimizedParams);
            let humanizedText = reasoningValidation.finalOutput;

            // Apply ULTRA-AGGRESSIVE post-processing transformations for maximum humanization
            if (modelType === 'deepseek-r1' && optimizedParams.targetDetection <= 10) {
                console.log('🔥 Applying ULTRA-AGGRESSIVE post-processing transformations...');
                const originalLength = humanizedText.length;
                humanizedText = applyUltraAggressiveTransformations(humanizedText);
                const transformationRate = ((originalLength - humanizedText.length) / originalLength * 100);
                console.log(`   Transformation applied: ${transformationRate.toFixed(1)}% text change`);
            }

            // Validate response quality
            if (!validateFalconResponse(humanizedText, prompt)) {
                throw new Error(`${modelType} model response failed quality validation`);
            }

            return {
                success: true,
                text: humanizedText,
                provider: provider.name,
                model: provider.model,
                usage: response.data.usage || {},
                processingTime: responseTime,
                modelType: modelType,
                optimizedParams: optimizedParams,
                reasoningValidation: reasoningValidation
            };
        } else {
            throw new Error('Invalid response format from API');
        }

    } catch (error) {
        console.error(`${provider.name} API error:`, error.message);

        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.response.data?.message || 'Unknown API error';

            // Handle Falcon-specific errors
            if (status === 503 && provider.name === 'huggingface') {
                throw new Error(`${provider.name} model loading (${status}): ${message}. Please retry in a few moments.`);
            }

            throw new Error(`${provider.name} API error (${status}): ${message}`);
        } else if (error.code === 'ECONNABORTED') {
            throw new Error(`${provider.name} API timeout - Falcon models may need more time`);
        } else {
            throw new Error(`${provider.name} API error: ${error.message}`);
        }
    }
}

/**
 * Fine-tuned parameters for DeepSeek-R1 and Falcon models optimized for ≤10% AI detection
 */
function optimizeParametersForModel(modelName, params) {
    const { temperature, topP, maxTokens, targetDetection = 10, enableDeepThink = false, reasoningTokens = 1000 } = params;

    // DeepSeek-R1: Primary model with DeepThink reasoning capabilities
    if (modelName.includes('deepseek-r1') || modelName.includes('DeepSeek-R1')) {
        // ULTRA-AGGRESSIVE parameter optimization for maximum transformation
        const baseTemp = targetDetection <= 5 ? 1.0 :     // Maximum creativity for ultra-strict
                        targetDetection <= 10 ? 0.98 :    // Very high for ≤10% target
                        0.95;                              // High for relaxed target

        const reasoningAllocation = enableDeepThink ? Math.min(reasoningTokens, maxTokens * 0.3) : 0;
        const outputTokens = maxTokens - reasoningAllocation;

        console.log(`💥 ULTRA-AGGRESSIVE DeepSeek-R1 Parameters:`);
        console.log(`   Target Detection: ≤${targetDetection}%`);
        console.log(`   DeepThink Enabled: ${enableDeepThink}`);
        console.log(`   Reasoning Tokens: ${reasoningAllocation}`);
        console.log(`   Output Tokens: ${outputTokens}`);
        console.log(`   Temperature: ${baseTemp} (MAXIMUM CREATIVITY)`);

        return {
            temperature: baseTemp,
            topP: 0.98, // Maximum creativity for radical transformation
            maxTokens: Math.min(outputTokens, 5000), // Increased for longer transformations
            repetitionPenalty: 1.0, // No penalty - let creativity flow
            presencePenalty: 0.7, // Maximum encouragement for topic diversity
            frequencyPenalty: 0.8, // Maximum word variation for human-like output
            reasoningTokens: reasoningAllocation,
            enableDeepThink: enableDeepThink,
            // Ultra-aggressive sampling for maximum transformation
            minP: 0.02, // Lower threshold for more creative sampling
            typicalP: 0.98, // Higher typical sampling for maximum randomness
            topK: 0 // Disable top-k completely
        };
    }

    // Falcon 3-7B: Balanced performance with optimized creativity
    if (modelName.includes('Falcon3-7B') || modelName.includes('falcon-3-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.85 : Math.min(temperature * 0.9, 0.8),
            topP: 0.88, // Slightly higher for more diverse vocabulary
            maxTokens: Math.min(maxTokens, 3800),
            repetitionPenalty: 1.15, // Increased to reduce AI-like repetition
            presencePenalty: 0.3, // Encourage topic diversity
            frequencyPenalty: 0.4 // Reduce word repetition
        };
    }

    // Falcon-H1-7B: Hybrid architecture optimized for efficiency and naturalness
    if (modelName.includes('Falcon-H1-7B') || modelName.includes('falcon-h1-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.82 : Math.min(temperature * 0.85, 0.75),
            topP: 0.86, // Focused but creative sampling
            maxTokens: Math.min(maxTokens, 3600),
            repetitionPenalty: 1.18, // Higher penalty for hybrid model
            presencePenalty: 0.25,
            frequencyPenalty: 0.35
        };
    }

    // Falcon 3-10B: High-performance model for demanding tasks
    if (modelName.includes('Falcon3-10B') || modelName.includes('falcon-3-10b')) {
        return {
            temperature: targetDetection <= 10 ? 0.88 : Math.min(temperature * 0.95, 0.85),
            topP: 0.90, // Higher creativity for larger model
            maxTokens: Math.min(maxTokens, 4000),
            repetitionPenalty: 1.12, // Lower penalty as larger model handles diversity better
            presencePenalty: 0.35,
            frequencyPenalty: 0.45
        };
    }

    // Falcon 180B: Premium model with maximum capabilities
    if (modelName.includes('falcon-180B') || modelName.includes('falcon-180b')) {
        return {
            temperature: targetDetection <= 10 ? 0.90 : Math.min(temperature * 1.0, 0.9),
            topP: 0.92, // Maximum creativity for largest model
            maxTokens: Math.min(maxTokens, 4500),
            repetitionPenalty: 1.08, // Minimal penalty for most capable model
            presencePenalty: 0.4,
            frequencyPenalty: 0.5
        };
    }



    // Llama optimizations
    if (modelName.includes('llama') || modelName.includes('Llama')) {
        return {
            temperature: targetDetection <= 10 ? temperature * 1.1 : temperature,
            topP: Math.min(topP, 0.9),
            maxTokens: maxTokens,
            repetitionPenalty: 1.05,
            presencePenalty: 0.15,
            frequencyPenalty: 0.25
        };
    }

    // Default parameters for unknown models
    return {
        temperature,
        topP,
        maxTokens,
        repetitionPenalty: 1.1,
        presencePenalty: 0.2,
        frequencyPenalty: 0.3
    };
}

/**
 * Enhanced synonym replacement system with contextual understanding
 * Redesigned for maximum effectiveness and natural language preservation
 */
function getAdvancedSynonyms(word, posTag, context = '') {
    // Enhanced synonym database with contextual awareness and naturalness scoring
    const synonymDatabase = {
        // Adjectives (JJ, JJR, JJS) - ordered by naturalness (most natural first)
        'important': {
            casual: ['key', 'big', 'major', 'main', 'crucial'],
            formal: ['significant', 'vital', 'essential', 'critical'],
            context_aware: {
                'business': ['key', 'critical', 'major'],
                'academic': ['significant', 'crucial', 'vital'],
                'casual': ['big', 'major', 'key']
            }
        },
        'significant': {
            casual: ['big', 'major', 'important', 'huge', 'massive'],
            formal: ['substantial', 'considerable', 'notable'],
            context_aware: {
                'data': ['big', 'major', 'huge'],
                'research': ['important', 'major', 'key'],
                'business': ['major', 'important', 'key']
            }
        },
        'comprehensive': {
            casual: ['complete', 'full', 'total', 'whole', 'entire'],
            formal: ['thorough', 'extensive', 'detailed'],
            context_aware: {
                'analysis': ['complete', 'full', 'thorough'],
                'study': ['complete', 'detailed', 'full'],
                'review': ['complete', 'full', 'total']
            }
        },
        'extensive': {
            casual: ['huge', 'massive', 'big', 'wide', 'large'],
            formal: ['comprehensive', 'broad', 'wide-ranging'],
            context_aware: {
                'research': ['huge', 'massive', 'big'],
                'data': ['massive', 'huge', 'big'],
                'analysis': ['big', 'wide', 'broad']
            }
        },
        'substantial': {
            casual: ['big', 'huge', 'large', 'major', 'massive'],
            formal: ['considerable', 'significant', 'notable'],
            context_aware: {
                'improvement': ['big', 'huge', 'major'],
                'change': ['big', 'major', 'huge'],
                'amount': ['big', 'large', 'huge']
            }
        },

        // Adverbs (RB, RBR, RBS) - transition words are critical for AI detection
        'furthermore': {
            casual: ['and', 'plus', 'also', 'on top of that'],
            formal: ['moreover', 'additionally', 'in addition'],
            context_aware: {
                'argument': ['and', 'plus', 'also'],
                'explanation': ['also', 'plus', 'on top of that'],
                'list': ['and', 'also', 'plus']
            }
        },
        'moreover': {
            casual: ['and', 'plus', 'also', 'what\'s more'],
            formal: ['furthermore', 'additionally'],
            context_aware: {
                'emphasis': ['and', 'plus', 'what\'s more'],
                'addition': ['also', 'plus', 'and']
            }
        },
        'consequently': {
            casual: ['so', 'that\'s why', 'which means', 'because of this'],
            formal: ['therefore', 'thus', 'as a result'],
            context_aware: {
                'cause_effect': ['so', 'that\'s why', 'which means'],
                'conclusion': ['so', 'that\'s why', 'because of this']
            }
        },

        // Verbs (VB, VBD, VBG, VBN, VBP, VBZ) - academic verbs are major AI indicators
        'utilize': {
            casual: ['use', 'work with', 'make use of', 'employ'],
            formal: ['employ', 'apply', 'implement'],
            context_aware: {
                'tool': ['use', 'work with', 'employ'],
                'method': ['use', 'apply', 'employ'],
                'resource': ['use', 'make use of', 'work with']
            }
        },
        'implement': {
            casual: ['do', 'carry out', 'put in place', 'set up'],
            formal: ['execute', 'deploy', 'establish'],
            context_aware: {
                'plan': ['carry out', 'do', 'put in place'],
                'system': ['set up', 'put in place', 'deploy'],
                'strategy': ['carry out', 'do', 'execute']
            }
        },
        'facilitate': {
            casual: ['help', 'make easier', 'assist', 'enable'],
            formal: ['enable', 'support', 'aid'],
            context_aware: {
                'process': ['help', 'make easier', 'assist'],
                'learning': ['help', 'assist', 'enable'],
                'communication': ['help', 'enable', 'assist']
            }
        },

        // Nouns (NN, NNS, NNP, NNPS) - technical nouns are AI indicators
        'methodology': {
            casual: ['method', 'approach', 'way', 'system'],
            formal: ['framework', 'procedure', 'protocol'],
            context_aware: {
                'research': ['method', 'approach', 'way'],
                'analysis': ['approach', 'method', 'system'],
                'study': ['method', 'way', 'approach']
            }
        },
        'implementation': {
            casual: ['setup', 'putting it in place', 'doing it', 'making it work'],
            formal: ['deployment', 'execution', 'installation'],
            context_aware: {
                'software': ['setup', 'installation', 'deployment'],
                'plan': ['putting it in place', 'doing it', 'execution'],
                'system': ['setup', 'deployment', 'installation']
            }
        }
    };

    const word_lower = word.toLowerCase();

    // Check if word exists in enhanced database
    if (synonymDatabase[word_lower]) {
        const synonymEntry = synonymDatabase[word_lower];

        // Contextual selection logic
        if (context && synonymEntry.context_aware) {
            for (const [contextType, synonyms] of Object.entries(synonymEntry.context_aware)) {
                if (context.toLowerCase().includes(contextType)) {
                    return synonyms[Math.floor(Math.random() * synonyms.length)];
                }
            }
        }

        // Default to casual synonyms for better humanization
        const synonyms = synonymEntry.casual || synonymEntry;
        return Array.isArray(synonyms) ?
               synonyms[Math.floor(Math.random() * synonyms.length)] :
               synonyms[Math.floor(Math.random() * synonyms.length)];
    }


    // Fallback: simple transformations for common patterns
    if (posTag && posTag.startsWith('JJ')) { // Adjectives
        if (word_lower.endsWith('ive')) {
            return word_lower.replace('ive', '') + 'y';
        }
        if (word_lower.endsWith('al')) {
            return word_lower.replace('al', '');
        }
    }

    return word; // Return original if no synonym found
}

/**
 * Advanced POS-aware word replacement with natural frequency distribution
 */
function applyAdvancedSynonymReplacement(text, replacementRate = 0.4) {
    // Simple POS tagging patterns (lightweight alternative to full NLP library)
    const posPatterns = {
        // Adjectives
        'JJ': /\b\w+(?:ive|al|ous|ful|less|able|ible|ant|ent|ic|ed)\b/g,
        // Adverbs
        'RB': /\b\w+(?:ly|ward|wise)\b/g,
        // Past participles (often used in passive voice)
        'VBN': /\b\w+(?:ed|en|d)\b/g,
        // Present participles
        'VBG': /\b\w+ing\b/g
    };

    // Split into words while preserving punctuation and spacing
    const words = text.match(/\b\w+\b|\W+/g) || [];
    let result = [];

    for (let i = 0; i < words.length; i++) {
        const word = words[i];

        // Skip non-word tokens
        if (!/\b\w+\b/.test(word)) {
            result.push(word);
            continue;
        }

        // Determine if we should replace this word
        if (Math.random() > replacementRate) {
            result.push(word);
            continue;
        }

        // Determine POS tag
        let posTag = 'NN'; // Default to noun
        for (const [tag, pattern] of Object.entries(posPatterns)) {
            if (pattern.test(word)) {
                posTag = tag;
                break;
            }
        }

        // Get synonym
        const synonym = getAdvancedSynonyms(word, posTag);

        // Preserve original capitalization
        if (word[0] === word[0].toUpperCase()) {
            result.push(synonym.charAt(0).toUpperCase() + synonym.slice(1));
        } else {
            result.push(synonym);
        }
    }

    return result.join('');
}

/**
 * Inject controlled human-like errors and imperfections
 * Enhanced based on techniques from Paraphraser repository
 */
function injectHumanLikeErrors(text, errorRate = 0.08) {
    let transformed = text;

    // 1. Occasional double spaces (very subtle but detectable by AI)
    transformed = transformed.replace(/\. /g, (match) => {
        return Math.random() < errorRate * 0.3 ? '.  ' : match;
    });

    // 2. Minor punctuation inconsistencies
    transformed = transformed.replace(/,(\w)/g, (match, letter) => {
        return Math.random() < errorRate * 0.2 ? `, ${letter}` : match;
    });

    // 3. Occasional informal contractions in formal text
    const informalContractions = {
        'going to': 'gonna',
        'want to': 'wanna',
        'have to': 'gotta',
        'out of': 'outta',
        'kind of': 'kinda',
        'sort of': 'sorta',
        'a lot of': 'lots of',
        'because': 'cause',
        'about': 'bout'
    };

    Object.entries(informalContractions).forEach(([formal, informal]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        transformed = transformed.replace(regex, (match) => {
            return Math.random() < errorRate * 0.12 ? informal : match;
        });
    });

    // 4. Subtle word order variations and casual intensifiers
    transformed = transformed.replace(/\b(very|really|quite|pretty)\s+(\w+)/g, (match, _adverb, adjective) => {
        if (Math.random() < errorRate * 0.15) {
            const casualIntensifiers = [
                `${adjective} as hell`,
                `super ${adjective}`,
                `crazy ${adjective}`,
                `insanely ${adjective}`,
                `ridiculously ${adjective}`
            ];
            return casualIntensifiers[Math.floor(Math.random() * casualIntensifiers.length)];
        }
        return match;
    });

    // 5. Add natural hesitation and filler words
    const fillerWords = ['um', 'uh', 'well', 'like', 'you know', 'I mean', 'actually'];
    transformed = transformed.replace(/\b(so|and|but)\s+/gi, (match) => {
        if (Math.random() < errorRate * 0.08) {
            const filler = fillerWords[Math.floor(Math.random() * fillerWords.length)];
            return `${match.trim()}, ${filler}, `;
        }
        return match;
    });

    // 6. Occasional typos that humans commonly make
    const commonTypos = {
        'the': ['teh', 'hte'],
        'and': ['adn', 'nad'],
        'with': ['wiht', 'whit'],
        'that': ['taht', 'htat'],
        'this': ['tihs', 'htis'],
        'from': ['form', 'fomr'],
        'they': ['tehy', 'htey'],
        'have': ['ahve', 'hvae'],
        'been': ['bene', 'eben'],
        'were': ['wer', 'weer']
    };

    Object.entries(commonTypos).forEach(([correct, typos]) => {
        const regex = new RegExp(`\\b${correct}\\b`, 'g');
        transformed = transformed.replace(regex, (match) => {
            if (Math.random() < errorRate * 0.02) { // Very low rate for typos
                const typo = typos[Math.floor(Math.random() * typos.length)];
                return typo;
            }
            return match;
        });
    });

    // 7. Natural repetition patterns (humans sometimes repeat words)
    transformed = transformed.replace(/\b(really|very|so|just|actually)\b/gi, (match) => {
        if (Math.random() < errorRate * 0.05) {
            return `${match} ${match.toLowerCase()}`;
        }
        return match;
    });

    // 8. Inconsistent capitalization after colons
    transformed = transformed.replace(/:\s*([A-Z])/g, (match, letter) => {
        if (Math.random() < errorRate * 0.3) {
            return `: ${letter.toLowerCase()}`;
        }
        return match;
    });

    // 9. Missing or extra commas in lists (subtle grammar errors)
    transformed = transformed.replace(/(\w+),\s*(\w+),\s*and\s+(\w+)/g, (match, word1, word2, word3) => {
        if (Math.random() < errorRate * 0.2) {
            return `${word1}, ${word2} and ${word3}`; // Missing Oxford comma
        }
        return match;
    });

    // 10. Casual abbreviations and shortcuts
    const casualAbbreviations = {
        'because': 'bc',
        'without': 'w/o',
        'with': 'w/',
        'between': 'b/w',
        'something': 'sth',
        'someone': 'sb',
        'probably': 'prob',
        'definitely': 'def'
    };

    Object.entries(casualAbbreviations).forEach(([full, abbrev]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        transformed = transformed.replace(regex, (match) => {
            return Math.random() < errorRate * 0.06 ? abbrev : match;
        });
    });

    return transformed;
}

/**
 * Apply ultra-aggressive post-processing transformations to ensure maximum humanization
 * Enhanced with advanced techniques from successful repositories
 */
function applyUltraAggressiveTransformations(text) {
    let transformed = text;

    // 1. Apply advanced synonym replacement first
    transformed = applyAdvancedSynonymReplacement(transformed, 0.45);

    // 2. DESTROY formal transitions completely (enhanced)
    const formalTransitions = {
        'furthermore': ['and', 'plus', 'also', 'on top of that', 'what\'s more', 'besides'],
        'moreover': ['and', 'plus', 'also', 'what\'s more', 'besides', 'on top of that'],
        'additionally': ['and', 'plus', 'also', 'on top of that', 'too', 'as well'],
        'consequently': ['so', 'which means', 'that\'s why', 'as a result', 'because of this'],
        'therefore': ['so', 'which means', 'that\'s why', 'hence', 'thus'],
        'thus': ['so', 'which means', 'that way', 'like this', 'in this way'],
        'hence': ['so', 'which is why', 'that\'s how', 'that\'s why', 'thus'],
        'subsequently': ['then', 'after that', 'next', 'later', 'afterwards'],
        'nevertheless': ['but', 'still', 'even so', 'however', 'yet'],
        'however': ['but', 'though', 'still', 'yet', 'even so'],
        'specifically': ['exactly', 'particularly', 'especially', 'in particular', 'precisely'],
        'essentially': ['basically', 'fundamentally', 'mainly', 'primarily', 'mostly'],
        'ultimately': ['finally', 'in the end', 'eventually', 'at last', 'ultimately']
    };

    Object.entries(formalTransitions).forEach(([formal, casual]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        transformed = transformed.replace(regex, () => {
            return casual[Math.floor(Math.random() * casual.length)];
        });
    });

    // 3. ELIMINATE robotic qualifiers (enhanced)
    const roboticQualifiers = {
        'it is important to note that': ['here\'s the thing -', 'look,', 'listen,', 'check this out -'],
        'it should be mentioned that': ['oh, and', 'by the way,', 'also,', 'another thing -'],
        'it is worth noting that': ['interesting thing is', 'what\'s cool is', 'here\'s what I noticed -', 'funny thing is'],
        'it must be emphasized that': ['seriously,', 'this is key -', 'here\'s what matters -', 'listen up -'],
        'it is essential to understand': ['you gotta know', 'here\'s the deal -', 'the thing is', 'look,'],
        'it should be understood that': ['basically,', 'the thing is', 'here\'s how it works -', 'so'],
        'it is crucial to recognize': ['you need to know', 'here\'s the key thing -', 'this is important -'],
        'it must be acknowledged': ['gotta admit', 'fair enough,', 'to be honest,', 'let\'s be real -']
    };

    Object.entries(roboticQualifiers).forEach(([robotic, human]) => {
        const regex = new RegExp(robotic.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        transformed = transformed.replace(regex, () => {
            return human[Math.floor(Math.random() * human.length)];
        });
    });

    // 4. INJECT massive contractions (enhanced)
    const contractions = {
        'do not': 'don\'t',
        'will not': 'won\'t',
        'cannot': 'can\'t',
        'should not': 'shouldn\'t',
        'would not': 'wouldn\'t',
        'could not': 'couldn\'t',
        'it is': 'it\'s',
        'that is': 'that\'s',
        'there is': 'there\'s',
        'we are': 'we\'re',
        'they are': 'they\'re',
        'you are': 'you\'re',
        'I am': 'I\'m',
        'he is': 'he\'s',
        'she is': 'she\'s',
        'we have': 'we\'ve',
        'they have': 'they\'ve',
        'I have': 'I\'ve',
        'you have': 'you\'ve',
        'I will': 'I\'ll',
        'you will': 'you\'ll',
        'we will': 'we\'ll',
        'they will': 'they\'ll'
    };

    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        transformed = transformed.replace(regex, contracted);
    });

    // 5. ADD human imperfections and casual language (enhanced)
    transformed = transformed.replace(/\. ([A-Z])/g, (match, letter) => {
        const connectors = ['. And ', '. But ', '. So ', '. Plus ', '. Also ', '. Oh, and ', '. By the way, '];
        if (Math.random() < 0.35) {
            return connectors[Math.floor(Math.random() * connectors.length)] + letter.toLowerCase();
        }
        return match;
    });

    // 6. INJECT casual qualifiers (enhanced)
    const casualQualifiers = [
        'pretty much', 'basically', 'sort of', 'kind of', 'more or less',
        'I think', 'probably', 'maybe', 'I guess', 'seems like',
        'apparently', 'supposedly', 'allegedly', 'presumably'
    ];

    transformed = transformed.replace(/\b(is|are|will|can|should|would|could)\b/g, (match) => {
        if (Math.random() < 0.25) {
            const qualifier = casualQualifiers[Math.floor(Math.random() * casualQualifiers.length)];
            return `${qualifier} ${match}`;
        }
        return match;
    });

    // 7. Apply adversarial attack patterns for AI detection evasion
    transformed = applyAdversarialAttackPatterns(transformed, 10);

    // 8. Inject controlled human-like errors
    transformed = injectHumanLikeErrors(transformed, 0.08);

    return transformed;
}

/**
 * Enhanced adversarial attack patterns for maximum AI detection evasion
 * Redesigned with sophisticated techniques for ≤10% detection achievement
 */
function applyAdversarialAttackPatterns(text, targetDetection = 10) {
    let transformed = text;

    // 1. Advanced semantic perturbations - meaning-preserving transformations
    const semanticTransformations = {
        // Sentence structure disruption
        'sentence_fragmentation': (text) => {
            return text.replace(/([^.!?]{60,}),\s*([^.!?]{30,})/g, (_match, part1, part2) => {
                const fragmenters = [
                    `${part1}. ${part2.charAt(0).toUpperCase()}${part2.slice(1)}`,
                    `${part1} - ${part2}`,
                    `${part1}. And ${part2}`,
                    `${part1}. Plus, ${part2}`,
                    `${part1}. Oh, and ${part2}`
                ];
                return fragmenters[Math.floor(Math.random() * fragmenters.length)];
            });
        },

        // Lexical substitution with context preservation
        'contextual_substitution': (text) => {
            const contextualMappings = {
                'shows': ['demonstrates', 'reveals', 'indicates', 'proves', 'displays'],
                'uses': ['employs', 'utilizes', 'applies', 'works with', 'makes use of'],
                'helps': ['assists', 'aids', 'supports', 'facilitates', 'enables'],
                'makes': ['creates', 'produces', 'generates', 'builds', 'forms'],
                'gives': ['provides', 'offers', 'supplies', 'delivers', 'presents']
            };

            Object.entries(contextualMappings).forEach(([original, alternatives]) => {
                const regex = new RegExp(`\\b${original}\\b`, 'gi');
                text = text.replace(regex, (match) => {
                    if (Math.random() < 0.4) {
                        const alt = alternatives[Math.floor(Math.random() * alternatives.length)];
                        return match[0] === match[0].toUpperCase() ?
                               alt.charAt(0).toUpperCase() + alt.slice(1) : alt;
                    }
                    return match;
                });
            });

            return text;
        },

        // Syntactic variation injection
        'syntactic_variation': (text) => {
            // Add natural discourse markers
            return text.replace(/^([A-Z][^.!?]*[.!?])/gm, (match) => {
                if (Math.random() < 0.25) {
                    const markers = ['Look, ', 'Listen, ', 'So ', 'Well, ', 'Actually, ', 'Honestly, ', 'To be fair, '];
                    const marker = markers[Math.floor(Math.random() * markers.length)];
                    return marker + match.charAt(0).toLowerCase() + match.slice(1);
                }
                return match;
            });
        }
    };

    // Apply semantic transformations based on detection target strictness
    const transformationIntensity = targetDetection <= 5 ? 0.8 : targetDetection <= 10 ? 0.6 : 0.4;

    Object.values(semanticTransformations).forEach(transform => {
        if (Math.random() < transformationIntensity) {
            transformed = transform(transformed);
        }
    });

    // 2. Advanced pattern obfuscation techniques
    const obfuscationTechniques = {
        // Micro-perturbations that confuse AI detectors
        'micro_perturbations': (text) => {
            // Strategic punctuation variations
            text = text.replace(/\. /g, (match) => {
                return Math.random() < 0.1 ? '.  ' : match; // Occasional double spaces
            });

            // Subtle capitalization inconsistencies
            text = text.replace(/:\s*([A-Z])/g, (match, letter) => {
                return Math.random() < 0.15 ? `: ${letter.toLowerCase()}` : match;
            });

            return text;
        },

        // Discourse marker injection for natural flow
        'discourse_markers': (text) => {
            const markers = {
                'addition': ['also', 'plus', 'and', 'on top of that', 'what\'s more'],
                'contrast': ['but', 'however', 'though', 'still', 'yet'],
                'emphasis': ['really', 'actually', 'honestly', 'seriously', 'definitely'],
                'clarification': ['I mean', 'that is', 'in other words', 'basically', 'essentially']
            };

            // Add markers at sentence boundaries
            text = text.replace(/\. ([A-Z])/g, (match, letter) => {
                if (Math.random() < 0.2) {
                    const markerType = Object.keys(markers)[Math.floor(Math.random() * Object.keys(markers).length)];
                    const marker = markers[markerType][Math.floor(Math.random() * markers[markerType].length)];
                    return `. ${marker.charAt(0).toUpperCase() + marker.slice(1)}, ${letter.toLowerCase()}`;
                }
                return match;
            });

            return text;
        },

        // Lexical complexity reduction
        'complexity_reduction': (text) => {
            const complexToSimple = {
                'demonstrate': 'show',
                'indicate': 'show',
                'illustrate': 'show',
                'establish': 'set up',
                'determine': 'find out',
                'investigate': 'look into',
                'examine': 'check out',
                'analyze': 'break down',
                'evaluate': 'judge',
                'assess': 'check'
            };

            Object.entries(complexToSimple).forEach(([complex, simple]) => {
                const regex = new RegExp(`\\b${complex}\\b`, 'gi');
                text = text.replace(regex, (match) => {
                    if (Math.random() < 0.7) { // High replacement rate
                        return match[0] === match[0].toUpperCase() ?
                               simple.charAt(0).toUpperCase() + simple.slice(1) : simple;
                    }
                    return match;
                });
            });

            return text;
        }
    };

    // Apply obfuscation techniques
    Object.values(obfuscationTechniques).forEach(technique => {
        transformed = technique(transformed);
    });

    // 3. Lexical diversity attacks - replace repeated words with varied alternatives
    const wordFrequency = {};
    const words = transformed.match(/\b\w+\b/g) || [];

    words.forEach(word => {
        const lowerWord = word.toLowerCase();
        wordFrequency[lowerWord] = (wordFrequency[lowerWord] || 0) + 1;
    });

    // Replace frequently repeated words with alternatives
    Object.entries(wordFrequency).forEach(([word, frequency]) => {
        if (frequency > 2 && word.length > 4) {
            const alternatives = getContextualAlternatives(word);
            if (alternatives.length > 0) {
                let replacementCount = 0;
                const maxReplacements = Math.floor(frequency / 2);

                transformed = transformed.replace(new RegExp(`\\b${word}\\b`, 'gi'), (match) => {
                    if (replacementCount < maxReplacements && Math.random() < 0.4) {
                        replacementCount++;
                        const alt = alternatives[Math.floor(Math.random() * alternatives.length)];
                        return match[0] === match[0].toUpperCase() ?
                               alt.charAt(0).toUpperCase() + alt.slice(1) : alt;
                    }
                    return match;
                });
            }
        }
    });

    // 4. Syntactic perturbations - vary sentence beginnings
    transformed = transformed.replace(/^([A-Z][^.!?]*[.!?])/gm, (match) => {
        const casualStarters = [
            'Look, ', 'Listen, ', 'So ', 'Well, ', 'Actually, ',
            'Honestly, ', 'To be fair, ', 'Here\'s the thing - '
        ];

        if (Math.random() < 0.2 && !match.toLowerCase().startsWith('the ')) {
            const starter = casualStarters[Math.floor(Math.random() * casualStarters.length)];
            return starter + match.charAt(0).toLowerCase() + match.slice(1);
        }
        return match;
    });

    // 5. Semantic perturbations - add redundant but natural qualifiers
    const qualifierPatterns = [
        {
            pattern: /\b(shows?|demonstrates?|proves?|indicates?)\b/gi,
            qualifiers: ['clearly shows', 'definitely proves', 'obviously demonstrates', 'totally indicates']
        },
        {
            pattern: /\b(helps?|assists?|supports?)\b/gi,
            qualifiers: ['really helps', 'actually assists', 'definitely supports', 'truly helps']
        }
    ];

    qualifierPatterns.forEach(({ pattern, qualifiers }) => {
        transformed = transformed.replace(pattern, (match) => {
            if (Math.random() < 0.25) {
                const qualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)];
                return qualifier;
            }
            return match;
        });
    });

    return transformed;
}

/**
 * Get contextual alternatives for lexical diversity attacks
 */
function getContextualAlternatives(word) {
    const alternatives = {
        'system': ['setup', 'framework', 'platform', 'structure', 'mechanism'],
        'method': ['approach', 'way', 'technique', 'process', 'procedure'],
        'process': ['procedure', 'method', 'approach', 'system', 'workflow'],
        'approach': ['method', 'way', 'technique', 'strategy', 'tactic'],
        'solution': ['answer', 'fix', 'resolution', 'remedy', 'way out'],
        'problem': ['issue', 'challenge', 'difficulty', 'trouble', 'concern'],
        'result': ['outcome', 'consequence', 'effect', 'product', 'end result'],
        'analysis': ['examination', 'study', 'review', 'assessment', 'evaluation'],
        'development': ['creation', 'building', 'construction', 'formation', 'growth'],
        'implementation': ['execution', 'deployment', 'installation', 'setup', 'rollout'],
        'performance': ['efficiency', 'effectiveness', 'operation', 'functioning', 'output'],
        'improvement': ['enhancement', 'upgrade', 'betterment', 'advancement', 'progress'],
        'information': ['data', 'details', 'facts', 'knowledge', 'intel'],
        'technology': ['tech', 'tools', 'systems', 'equipment', 'machinery'],
        'organization': ['company', 'business', 'firm', 'enterprise', 'corporation']
    };

    return alternatives[word.toLowerCase()] || [];
}

/**
 * Validate reasoning chain for DeepSeek-R1 responses
 */
function validateReasoningChain(fullResponse, modelType, optimizedParams) {
    const validation = {
        hasReasoning: false,
        reasoningQuality: 0,
        finalOutput: fullResponse,
        reasoningLength: 0,
        reasoningSteps: 0
    };

    if (modelType === 'deepseek-r1' && optimizedParams.enableDeepThink) {
        // Check for reasoning tags
        const thinkMatch = fullResponse.match(/<think>([\s\S]*?)<\/think>/);

        if (thinkMatch) {
            validation.hasReasoning = true;
            const reasoningContent = thinkMatch[1].trim();
            validation.reasoningLength = reasoningContent.length;

            // Count reasoning steps
            validation.reasoningSteps = (reasoningContent.match(/STEP \d+/g) || []).length;

            // Extract final output (everything after </think>)
            validation.finalOutput = fullResponse.replace(/<think>[\s\S]*?<\/think>\s*/, '').trim();

            // Assess reasoning quality based on new aggressive approach
            const qualityIndicators = [
                reasoningContent.includes('AGGRESSIVE AI PATTERN ELIMINATION'),
                reasoningContent.includes('RADICAL HUMAN VOICE INJECTION'),
                reasoningContent.includes('EXTREME RESTRUCTURING STRATEGY'),
                reasoningContent.includes('ZERO TOLERANCE VALIDATION'),
                reasoningContent.includes('COMPLETELY TRANSFORM'),
                reasoningContent.includes('AGGRESSIVELY transform'),
                validation.reasoningSteps >= 4,
                validation.reasoningLength > 300
            ];

            validation.reasoningQuality = qualityIndicators.filter(Boolean).length / qualityIndicators.length;

            console.log(`🧠 DeepThink Reasoning Validation:`);
            console.log(`   Reasoning Found: ${validation.hasReasoning}`);
            console.log(`   Reasoning Length: ${validation.reasoningLength} chars`);
            console.log(`   Reasoning Steps: ${validation.reasoningSteps}`);
            console.log(`   Quality Score: ${(validation.reasoningQuality * 100).toFixed(1)}%`);

        } else {
            console.log(`⚠️  DeepThink enabled but no reasoning chain found in response`);
        }
    }

    return validation;
}

/**
 * Validate model response quality
 */
function validateFalconResponse(response, originalPrompt) {
    if (!response || response.length < 10) {
        return false;
    }

    // Check for common Falcon model issues
    if (response.includes('I cannot') || response.includes('I apologize')) {
        return false;
    }

    // Check for repetitive patterns (common in Falcon models)
    const sentences = response.split(/[.!?]+/);
    if (sentences.length > 3) {
        const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()));
        if (uniqueSentences.size / sentences.length < 0.7) {
            return false; // Too repetitive
        }
    }

    return true;
}

/**
 * Advanced Performance Optimization System
 * Implements LRU cache, pre-computed lookups, and optimized data structures
 */

// LRU Cache implementation for memory-efficient caching
class LRUCache {
    constructor(maxSize = 1000) {
        this.maxSize = maxSize;
        this.cache = new Map();
    }

    get(key) {
        if (this.cache.has(key)) {
            // Move to end (most recently used)
            const value = this.cache.get(key);
            this.cache.delete(key);
            this.cache.set(key, value);
            return value;
        }
        return null;
    }

    set(key, value) {
        if (this.cache.has(key)) {
            this.cache.delete(key);
        } else if (this.cache.size >= this.maxSize) {
            // Remove least recently used (first item)
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }
        this.cache.set(key, value);
    }

    clear() {
        this.cache.clear();
    }

    size() {
        return this.cache.size;
    }
}

// Optimized caching system with different cache sizes per tier
const optimizedCaches = {
    synonym: new LRUCache(2000), // Larger cache for synonyms
    pos: new LRUCache(1000),     // Medium cache for POS tags
    word: new LRUCache(500),     // Smaller cache for word analysis
    sentence: new LRUCache(100)  // Small cache for sentence patterns
};

// Pre-computed common word sets for O(1) lookup
const commonWordsSet = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
    'between', 'among', 'under', 'over', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
    'might', 'must', 'can', 'shall', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
    'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
    'her', 'its', 'our', 'their'
]);

// Pre-compiled regex patterns for better performance
const optimizedRegexPatterns = {
    sentences: /[^.!?]*[.!?]+\s*|[^.!?]+$/g,
    tokens: /\w+|[^\w\s]+/g,
    words: /\w+/g,
    wordToken: /^\w+$/,
    wordStart: /^\w/,
    wordEnd: /\w$/,
    adjective: /(ful|less|ous|ive|able|ible|al|ic|ed|ing)$/,
    adverb: /(ly|ward|wise)$/,
    verb: /(ed|ing|s)$/
};

/**
 * Enhanced user tier-based performance configuration with advanced optimizations
 */
const PERFORMANCE_TIERS = {
    free: {
        processingDelay: 500, // Reduced delay for better UX
        batchSize: 20,
        cacheEnabled: false,
        parallelProcessing: false,
        maxCacheSize: 0,
        useOptimizedRegex: false,
        usePrecomputedSets: false,
        algorithmComplexity: 'basic'
    },
    premium: {
        processingDelay: 0,
        batchSize: 100,
        cacheEnabled: true,
        parallelProcessing: true,
        maxCacheSize: 1000,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        useLRUCache: true,
        algorithmComplexity: 'optimized'
    },
    admin: {
        processingDelay: 0,
        batchSize: 200,
        cacheEnabled: true,
        parallelProcessing: true,
        optimizedAlgorithms: true,
        maxCacheSize: 2000,
        useOptimizedRegex: true,
        usePrecomputedSets: true,
        useLRUCache: true,
        useAdvancedOptimizations: true,
        algorithmComplexity: 'maximum'
    }
};

/**
 * Detect user tier from session or request context with improved logic
 */
function getUserTier(options = {}) {
    // Check for explicit tier in options (for testing)
    if (options.userTier) {
        return options.userTier;
    }

    // Check for user session data
    if (options.user) {
        const subscriptionTier = options.user.subscriptionTier || 'free';
        const userRole = options.user.role || 'user';

        // Map subscription tiers to performance tiers
        if (userRole === 'admin' || userRole === 'owner') return 'admin';
        if (subscriptionTier.includes('premium') || subscriptionTier.includes('pro')) return 'premium';
    }

    // Default to free tier
    return 'free';
}

/**
 * NLTK-inspired humanization using JavaScript NLP techniques
 * Now with advanced performance optimizations and user tier support
 */
export async function humanizeWithNLTKApproach(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        useAdvancedSynonyms = true,
        user = null // User session for tier detection
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    const userTier = getUserTier({ user, ...options });
    const performanceConfig = PERFORMANCE_TIERS[userTier];

    try {
        console.log(`🔬 Starting NLTK-inspired humanization (${userTier} tier)...`);

        // Apply processing delay for free users (feature limitation)
        if (performanceConfig.processingDelay > 0) {
            console.log(`⏳ Processing delay: ${performanceConfig.processingDelay}ms for ${userTier} users...`);
            await new Promise(resolve => setTimeout(resolve, performanceConfig.processingDelay));
        }

        // Apply NLTK-inspired processing with tier-specific optimizations
        const humanizedText = await processTextWithNLTKApproachOptimized(text, {
            aggressiveness,
            maintainTone,
            useAdvancedSynonyms,
            performanceConfig,
            userTier
        });

        const processingTime = Date.now() - startTime;

        return {
            success: true,
            text: humanizedText,
            originalText: text,
            method: 'nltk-inspired',
            processingTime,
            originalLength: text.length,
            newLength: humanizedText.length,
            transformationRate: ((text.length - humanizedText.length) / text.length * 100).toFixed(1),
            userTier,
            performanceOptimizations: {
                cacheEnabled: performanceConfig.cacheEnabled,
                parallelProcessing: performanceConfig.parallelProcessing,
                batchSize: performanceConfig.batchSize,
                algorithmComplexity: performanceConfig.algorithmComplexity
            }
        };

    } catch (error) {
        console.error('NLTK-inspired humanization failed:', error);
        return {
            success: false,
            error: error.message,
            originalText: text,
            method: 'nltk-inspired',
            userTier
        };
    }
}

/**
 * Main humanization function using advanced LLMs
 */
export async function humanizeWithAdvancedLLM(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        preferredModel = 'deepseek-r1',
        maxRetries = 2,
        useNLTKApproach = false // New option to use NLTK-inspired processing
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    // Use NLTK-inspired approach if requested
    if (useNLTKApproach) {
        return await humanizeWithNLTKApproach(text, options);
    }

    const startTime = Date.now();

    // Analyze content type for better prompting
    const contentType = analyzeContentType(text);

    // Generate optimized prompt
    const prompt = generateHumanizationPrompt(text, {
        aggressiveness,
        maintainTone,
        targetDetection,
        contentType
    });

    // Enhanced intelligent model selection with content analysis
    const modelOrder = getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text);

    console.log(`🧠 DEEPSEEK-R1 MODEL SELECTION DEBUG:`);
    console.log(`   Preferred Model: ${preferredModel}`);
    console.log(`   Target Detection: ≤${targetDetection}%`);
    console.log(`   Model Order: ${modelOrder.join(' → ')}`);
    console.log(`   DeepSeek-R1 Priority: ${modelOrder.indexOf('deepseek-r1') + 1}/${modelOrder.length}`);

    for (const modelName of modelOrder) {
        const modelConfig = MODEL_CONFIGS[modelName];
        if (!modelConfig) {
            console.log(`❌ Model config not found: ${modelName}`);
            continue;
        }

        console.log(`\n🔄 Trying model: ${modelName}`);

        // Sort providers by priority
        const sortedProviders = modelConfig.providers.sort((a, b) => (a.priority || 999) - (b.priority || 999));
        console.log(`   Available providers: ${sortedProviders.map(p => p.name).join(', ')}`);

        // Try each provider for this model
        for (const provider of sortedProviders) {
            // Check if API key is available
            if (!process.env[provider.apiKeyEnv]) {
                console.log(`   ⚠️  Skipping ${provider.name}: API key not configured (${provider.apiKeyEnv})`);
                continue;
            }

            console.log(`   ✅ Using ${provider.name} for ${modelName}`);

            let retries = 0;
            while (retries <= maxRetries) {
                try {
                    // Determine model type and DeepThink activation
                    const isDeepSeekR1 = modelName.includes('deepseek-r1') || modelName.includes('deepseek');
                    const modelType = isDeepSeekR1 ? 'deepseek-r1' :
                                     modelName.includes('falcon') ? 'falcon' : 'other';

                    // Enable DeepThink for DeepSeek-R1
                    const enableDeepThink = isDeepSeekR1 && targetDetection <= 10;

                    if (enableDeepThink) {
                        console.log(`   🧠 DeepThink ACTIVATED for ${modelName} (≤${targetDetection}% target)`);
                    }

                    const result = await callLLMAPI(provider, prompt, {
                        maxTokens: Math.min(4500, text.length * 3), // Increased for DeepSeek-R1 reasoning
                        temperature: calculateOptimalTemperature(aggressiveness, modelType),
                        topP: isDeepSeekR1 ? 0.95 : (modelType === 'falcon' ? 0.85 : 0.9),
                        modelType: modelType,
                        targetDetection: targetDetection,
                        enableDeepThink: enableDeepThink,
                        reasoningTokens: enableDeepThink ? 1000 : 0
                    });

                    const totalTime = Date.now() - startTime;

                    console.log(`Successfully humanized with ${provider.name}/${modelName} in ${totalTime}ms`);

                    // Multi-pass processing for ≤10% AI detection targets
                    let finalResult = result;
                    if (targetDetection <= 10 && result.success) {
                        console.log(`Applying multi-pass refinement for ≤${targetDetection}% detection target...`);
                        finalResult = await applyMultiPassRefinement(result, {
                            originalText: text,
                            targetDetection,
                            aggressiveness,
                            modelName,
                            provider
                        });
                    }

                    // Real-time AI detection validation for ≤10% targets
                    let detectionValidation = null;
                    if (targetDetection <= 10 && isRealTimeDetectionAvailable()) {
                        console.log('Performing real-time AI detection validation...');
                        detectionValidation = await validateWithRealTimeDetection(finalResult.text, {
                            targetDetection,
                            preferredAPI: 'gptzero',
                            fallbackAPIs: ['originality', 'sapling']
                        });

                        if (detectionValidation.success && !detectionValidation.meetsTarget) {
                            console.log(`Real-time detection: ${detectionValidation.score.toFixed(1)}% > ${targetDetection}%`);

                            // Auto-retry with higher aggressiveness if detection score is too high
                            if (detectionValidation.recommendation.shouldRetry && retries === 0) {
                                console.log('Auto-retrying with increased aggressiveness...');
                                const newAggressiveness = Math.min(aggressiveness + detectionValidation.recommendation.suggestedAggressiveness, 1.0);

                                // Recursive retry with higher aggressiveness
                                return await humanizeWithAdvancedLLM(text, {
                                    aggressiveness: newAggressiveness,
                                    maintainTone,
                                    targetDetection,
                                    preferredModel: modelName,
                                    maxRetries: 0 // Prevent infinite recursion
                                });
                            }
                        }
                    }

                    // Advanced quality control with comprehensive validation
                    const qualityValidation = validateTextQuality(finalResult.text, targetDetection);
                    const validationResult = detectionValidation || await validateDetectionTarget(finalResult.text, targetDetection);
                    const meetsTarget = detectionValidation ? detectionValidation.meetsTarget : validationResult;
                    const meetsQualityStandards = qualityValidation.overallQuality;

                    // Additional quality checks for commercial-grade requirements
                    const readabilityCheck = assessReadability(finalResult.text);
                    const transformationCheck = assessTransformationQuality(text, finalResult.text);
                    const professionalToneCheck = assessProfessionalTone(finalResult.text);

                    console.log(`Quality validation: Composite score ${qualityValidation.compositeScore}/100, Perplexity: ${qualityValidation.perplexity.score}/100 (${qualityValidation.perplexity.quality})`);
                    console.log(`Readability: ${readabilityCheck.score}/100, Transformation: ${transformationCheck.rate}%, Professional tone: ${professionalToneCheck.maintained ? 'YES' : 'NO'}`);

                    const passesAllQualityChecks = meetsTarget && meetsQualityStandards &&
                                                  readabilityCheck.score >= 70 &&
                                                  transformationCheck.rate >= 60 &&
                                                  professionalToneCheck.maintained;

                    if (passesAllQualityChecks) {
                        return {
                            ...finalResult,
                            originalText: text,
                            processingTime: totalTime + (finalResult.refinementTime || 0),
                            method: finalResult.multiPass ? 'llm-enhanced-nuclear-multipass' : 'llm-enhanced-nuclear',
                            modelName,
                            detectionTarget: targetDetection,
                            detectionValidation: detectionValidation,
                            qualityValidation: qualityValidation,
                            readabilityCheck: readabilityCheck,
                            transformationCheck: transformationCheck,
                            professionalToneCheck: professionalToneCheck,
                            overallQualityScore: Math.round((qualityValidation.compositeScore + readabilityCheck.score + transformationCheck.rate) / 3),
                            options: { aggressiveness, maintainTone, targetDetection }
                        };
                    } else {
                        const score = detectionValidation ? detectionValidation.score : 'unknown';
                        console.warn(`${modelName} result detection score ${score}% exceeds ≤${targetDetection}% target, trying next model`);
                        break; // Try next model instead of retrying same model
                    }

                } catch (error) {
                    retries++;
                    console.warn(`${provider.name} attempt ${retries}/${maxRetries + 1} failed:`, error.message);

                    if (retries <= maxRetries) {
                        // Progressive backoff with longer delays for Falcon models
                        const delay = modelType === 'falcon' ? 2000 * retries : 1000 * retries;
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
        }
    }

    // If all LLM attempts failed
    const totalTime = Date.now() - startTime;
    return {
        success: false,
        error: 'All advanced LLM providers failed',
        originalText: text,
        processingTime: totalTime,
        method: 'failed',
        fallbackRecommended: true
    };
}

/**
 * Enhanced content type analysis for better Falcon model prompting
 */
function analyzeContentType(text) {
    const formalIndicators = /\b(therefore|furthermore|consequently|moreover|nevertheless|thus|hence)\b/gi;
    const technicalIndicators = /\b(API|algorithm|implementation|configuration|optimization|framework|architecture|deployment)\b/gi;
    const academicIndicators = /\b(research|study|analysis|methodology|findings|hypothesis|conclusion|evidence)\b/gi;
    const businessIndicators = /\b(strategy|revenue|market|customer|business|sales|profit|ROI)\b/gi;
    const creativeIndicators = /\b(story|narrative|creative|artistic|design|aesthetic|inspiration)\b/gi;

    if (academicIndicators.test(text)) return 'academic';
    if (technicalIndicators.test(text)) return 'technical';
    if (businessIndicators.test(text)) return 'business';
    if (creativeIndicators.test(text)) return 'creative';
    if (formalIndicators.test(text)) return 'formal';
    return 'general';
}

/**
 * Enhanced intelligent model selection based on content analysis and target detection
 */
function getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text = '') {
    // Analyze content for AI patterns to determine optimal model selection
    const contentAnalysis = analyzeContentComplexity(text);

    // For ≤10% AI detection target, prioritize DeepSeek-R1 with DeepThink reasoning
    if (targetDetection <= 10) {
        console.log(`Content analysis: AI risk=${contentAnalysis.aiRisk}, complexity=${contentAnalysis.complexity}, length=${contentAnalysis.length}`);

        // High AI risk content - DeepSeek-R1 first with Falcon fallbacks
        if (contentAnalysis.aiRisk >= 7 || aggressiveness >= 0.8) {
            console.log('High AI risk detected, using DeepSeek-R1 with maximum power fallbacks');
            return ['deepseek-r1', 'falcon-180b', 'falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', preferredModel];
        }

        // Medium AI risk with complex content - DeepSeek-R1 primary
        if (contentAnalysis.aiRisk >= 4 || contentAnalysis.complexity >= 6 || aggressiveness >= 0.6) {
            console.log('Medium-high AI risk, using DeepSeek-R1 with powerful fallbacks');
            return ['deepseek-r1', 'falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-180b', preferredModel];
        }

        // Standard ≤10% detection with moderate content - DeepSeek-R1 first
        if (aggressiveness >= 0.4) {
            console.log('Standard ≤10% detection, using DeepSeek-R1 with balanced fallbacks');
            return ['deepseek-r1', 'falcon-3-7b', 'falcon-h1-7b', 'falcon-3-10b', preferredModel, 'llama-3.1-8b'];
        }

        // Low aggressiveness but still ≤10% target - DeepSeek-R1 primary
        console.log('Low aggressiveness ≤10% target, using DeepSeek-R1 with efficient fallbacks');
        return ['deepseek-r1', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-3-10b', preferredModel];
    }

    // For higher detection targets (>10%), still prioritize DeepSeek-R1 but with faster fallbacks
    if (targetDetection <= 20) {
        return ['deepseek-r1', 'falcon-3-7b', preferredModel, 'falcon-h1-7b', 'llama-3.1-8b', 'mistral-7b'];
    }

    // For relaxed detection targets (>20%), DeepSeek-R1 first for consistency
    return ['deepseek-r1', preferredModel, 'falcon-3-7b', 'llama-3.1-8b', 'mistral-7b'];
}

/**
 * Analyze content complexity and AI risk patterns
 */
function analyzeContentComplexity(text) {
    if (!text || typeof text !== 'string') {
        return { aiRisk: 0, complexity: 0, length: 0 };
    }

    let aiRisk = 0;
    let complexity = 0;

    // AI risk pattern analysis
    const highRiskPatterns = [
        /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly)\b/gi,
        /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized)\b/gi,
        /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly)\b/gi,
        /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various)\b/gi
    ];

    const mediumRiskPatterns = [
        /\b(clearly|obviously|certainly|definitely|undoubtedly|unquestionably)\b/gi,
        /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
        /\b(implementation|optimization|utilization|maximization|minimization)\b/gi
    ];

    // Count high-risk patterns (weight: 2)
    highRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length * 2;
    });

    // Count medium-risk patterns (weight: 1)
    mediumRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length;
    });

    // Complexity analysis
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    const words = text.split(/\s+/).length;
    const uniqueWords = new Set(text.toLowerCase().split(/\s+/)).size;
    const lexicalDiversity = uniqueWords / words;

    // Calculate complexity score (0-10)
    if (avgSentenceLength > 35) complexity += 2;
    if (avgSentenceLength > 50) complexity += 2;
    if (words > 500) complexity += 1;
    if (words > 1000) complexity += 2;
    if (lexicalDiversity < 0.4) complexity += 2; // Low diversity = more complex to humanize
    if (lexicalDiversity < 0.3) complexity += 1;

    // Technical content indicators
    const technicalPatterns = [
        /\b(API|algorithm|implementation|configuration|optimization|framework|architecture)\b/gi,
        /\b(methodology|analysis|evaluation|assessment|validation|verification)\b/gi
    ];

    technicalPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        complexity += Math.min(matches.length, 3); // Cap technical complexity contribution
    });

    return {
        aiRisk: Math.min(aiRisk, 10), // Cap at 10
        complexity: Math.min(complexity, 10), // Cap at 10
        length: text.length,
        avgSentenceLength: avgSentenceLength.toFixed(1),
        lexicalDiversity: lexicalDiversity.toFixed(3),
        wordCount: words
    };
}

/**
 * Calculate optimal parameters for enhanced humanization performance
 * Redesigned for consistent ≤10% AI detection achievement
 */
function calculateOptimalParameters(aggressiveness, modelType, targetDetection, passNumber = 1) {
    // Enhanced aggressiveness scaling for better results
    const scaledAggressiveness = Math.min(aggressiveness + (passNumber - 1) * 0.1, 1.0);

    // Base temperature calculation with model-specific optimization
    let temperature;
    if (modelType === 'deepseek-r1') {
        // DeepSeek-R1 performs better with higher temperature for creative humanization
        temperature = Math.min(0.85 + (scaledAggressiveness * 0.15), 0.98);
    } else if (modelType === 'falcon') {
        // Falcon models work better with moderate temperature
        temperature = Math.min(0.7 + (scaledAggressiveness * 0.2), 0.85);
    } else {
        // Default calculation for other models
        temperature = Math.min(0.6 + (scaledAggressiveness * 0.3), 0.9);
    }

    // Enhanced top_p for better diversity
    const topP = modelType === 'deepseek-r1' ?
                 Math.min(0.9 + (scaledAggressiveness * 0.08), 0.98) :
                 Math.min(0.85 + (scaledAggressiveness * 0.1), 0.95);

    // Repetition penalty optimization
    const repetitionPenalty = modelType === 'falcon' ?
                             1.1 + (scaledAggressiveness * 0.15) :
                             1.05 + (scaledAggressiveness * 0.1);

    // Max tokens based on target detection strictness
    const maxTokensMultiplier = targetDetection <= 5 ? 3.0 :
                               targetDetection <= 10 ? 2.5 : 2.0;

    return {
        temperature,
        topP,
        repetitionPenalty,
        maxTokensMultiplier,
        scaledAggressiveness
    };
}

/**
 * Enhanced multi-pass refinement system for sophisticated humanization
 * Iteratively improves text by analyzing and eliminating AI patterns
 */
async function applyMultiPassRefinement(initialResult, options) {
    const { targetDetection } = options;
    const startTime = Date.now();
    let currentResult = initialResult;
    let passCount = 0;
    const maxPasses = 3;

    try {
        console.log(`🔄 Starting multi-pass refinement (target: ≤${targetDetection}%)`);

        while (passCount < maxPasses) {
            passCount++;
            console.log(`\n--- Pass ${passCount}/${maxPasses} ---`);

            // Analyze current result for remaining AI patterns
            const aiAnalysis = analyzeRemainingAIPatterns(currentResult.text);
            console.log(`AI risk score: ${aiAnalysis.riskScore}/10`);

            // Enhanced stopping criteria - much more aggressive threshold
            const targetRiskScore = targetDetection <= 5 ? 1 : 2; // Stricter for commercial grade
            if (aiAnalysis.riskScore <= targetRiskScore) {
                console.log(`✅ Pass ${passCount}: Risk score ${aiAnalysis.riskScore} ≤ ${targetRiskScore}, refinement complete`);
                break;
            }

            // Enhanced adaptive post-processing based on analysis and pass number
            let refinedText = currentResult.text;
            const aggressivenessBoost = passCount * 0.1; // Increase aggressiveness each pass

            // Pass 1: Nuclear pattern elimination and structural overhaul
            if (passCount === 1) {
                console.log('Pass 1: Nuclear pattern elimination and structural overhaul');
                refinedText = applyNuclearPatternElimination(refinedText, aiAnalysis);
                refinedText = applyStructuralRefinements(refinedText, aiAnalysis);
                refinedText = applyLexicalDiversityEnhancements(refinedText);
                refinedText = applyAdvancedSynonymReplacement(refinedText, 0.6 + aggressivenessBoost);
            }

            // Pass 2: Aggressive syntactic transformation and flow optimization
            else if (passCount === 2) {
                console.log('Pass 2: Aggressive syntactic transformation and flow optimization');
                refinedText = applySyntacticVariations(refinedText, aiAnalysis);
                refinedText = improveTextualFlow(refinedText);
                refinedText = applyAdversarialAttackPatterns(refinedText, options.targetDetection);
                refinedText = injectAdvancedCasualLanguage(refinedText, 0.3 + aggressivenessBoost);
            }

            // Pass 3: Final nuclear humanization with maximum transformation
            else if (passCount === 3) {
                console.log('Pass 3: Final nuclear humanization with maximum transformation');
                refinedText = applyFinalHumanizationPolish(refinedText, aiAnalysis);
                refinedText = applyUltraAggressiveTransformations(refinedText);
                refinedText = injectHumanLikeErrors(refinedText, 0.1 + aggressivenessBoost);
                refinedText = applyFinalQualityEnhancement(refinedText, aiAnalysis);
            }

            // Update current result
            currentResult = {
                ...currentResult,
                text: refinedText,
                multiPass: true,
                passCount: passCount
            };

            // Check improvement
            const newAnalysis = analyzeRemainingAIPatterns(refinedText);
            const improvement = aiAnalysis.riskScore - newAnalysis.riskScore;
            console.log(`Pass ${passCount} improvement: ${improvement.toFixed(1)} points (${aiAnalysis.riskScore} → ${newAnalysis.riskScore})`);

            // If no significant improvement, stop
            if (improvement < 0.5 && passCount > 1) {
                console.log('Minimal improvement detected, stopping refinement');
                break;
            }
        }

        const refinementTime = Date.now() - startTime;
        console.log(`🎯 Multi-pass refinement completed in ${refinementTime}ms (${passCount} passes)`);

        return {
            ...currentResult,
            refinementTime: refinementTime,
            totalPasses: passCount,
            finalRiskScore: analyzeRemainingAIPatterns(currentResult.text).riskScore
        };

    } catch (error) {
        console.error('Multi-pass refinement error:', error.message);
        return initialResult; // Return original result if refinement fails
    }
}

/**
 * Apply structural refinements to eliminate formal patterns
 */
function applyStructuralRefinements(text, aiAnalysis) {
    let refined = text;

    // Target specific structural issues identified in analysis
    aiAnalysis.issues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                // Already handled in main transformation, but add more aggressive replacements
                refined = refined.replace(/\b(furthermore|moreover|additionally)\b/gi, () => {
                    const casual = ['and', 'plus', 'also', 'oh, and', 'by the way'];
                    return casual[Math.floor(Math.random() * casual.length)];
                });
                break;

            case 'mechanicalConclusions':
                refined = refined.replace(/\b(in conclusion|to summarize|in summary)\b/gi, () => {
                    const casual = ['so basically', 'bottom line', 'long story short', 'anyway'];
                    return casual[Math.floor(Math.random() * casual.length)];
                });
                break;

            case 'passiveOveruse':
                // Convert passive to active voice more aggressively
                refined = refined.replace(/\b(is|are|was|were)\s+(\w+ed)\b/gi, (match, _verb, pastParticiple) => {
                    if (Math.random() < 0.6) {
                        const activeAlternatives = [
                            `people ${pastParticiple.replace('ed', '')}`,
                            `we ${pastParticiple.replace('ed', '')}`,
                            `they ${pastParticiple.replace('ed', '')}`
                        ];
                        return activeAlternatives[Math.floor(Math.random() * activeAlternatives.length)];
                    }
                    return match;
                });
                break;
        }
    });

    return refined;
}

/**
 * Apply lexical diversity enhancements
 */
function applyLexicalDiversityEnhancements(text) {
    let enhanced = text;

    // Replace overused academic/formal words with varied alternatives
    const diversityReplacements = {
        'significant': ['big', 'major', 'important', 'key', 'huge', 'massive'],
        'substantial': ['big', 'large', 'major', 'considerable', 'hefty'],
        'comprehensive': ['complete', 'full', 'thorough', 'detailed', 'in-depth'],
        'extensive': ['wide', 'broad', 'large', 'huge', 'massive', 'wide-ranging'],
        'numerous': ['many', 'lots of', 'tons of', 'plenty of', 'loads of'],
        'various': ['different', 'many', 'several', 'multiple', 'all sorts of'],
        'utilize': ['use', 'employ', 'work with', 'make use of'],
        'implement': ['do', 'carry out', 'put in place', 'set up'],
        'facilitate': ['help', 'make easier', 'assist', 'enable']
    };

    Object.entries(diversityReplacements).forEach(([formal, alternatives]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        enhanced = enhanced.replace(regex, (match) => {
            if (Math.random() < 0.7) {
                const alt = alternatives[Math.floor(Math.random() * alternatives.length)];
                return match[0] === match[0].toUpperCase() ?
                       alt.charAt(0).toUpperCase() + alt.slice(1) : alt;
            }
            return match;
        });
    });

    return enhanced;
}

/**
 * Apply syntactic variations to break AI detection patterns
 */
function applySyntacticVariations(text, _aiAnalysis) {
    let varied = text;

    // Break up long sentences more aggressively
    varied = varied.replace(/([^.!?]{80,}),\s*([^.!?]{30,})/g, (_match, part1, part2) => {
        const breakPatterns = [
            `${part1}. ${part2.charAt(0).toUpperCase()}${part2.slice(1)}`,
            `${part1} - ${part2}`,
            `${part1}. And ${part2}`,
            `${part1}. Plus, ${part2}`
        ];
        return breakPatterns[Math.floor(Math.random() * breakPatterns.length)];
    });

    // Add more sentence variety
    varied = varied.replace(/^([A-Z][^.!?]*[.!?])/gm, (match) => {
        if (Math.random() < 0.3) {
            const starters = ['Look, ', 'Listen, ', 'So ', 'Well, ', 'Actually, ', 'Honestly, '];
            const starter = starters[Math.floor(Math.random() * starters.length)];
            return starter + match.charAt(0).toLowerCase() + match.slice(1);
        }
        return match;
    });

    return varied;
}

/**
 * Improve textual flow with natural transitions
 */
function improveTextualFlow(text) {
    let improved = text;

    // Add natural connectors between sentences
    improved = improved.replace(/\.\s+([A-Z])/g, (match, letter) => {
        if (Math.random() < 0.25) {
            const connectors = ['. And ', '. But ', '. So ', '. Plus, ', '. Also, ', '. Oh, and '];
            const connector = connectors[Math.floor(Math.random() * connectors.length)];
            return connector + letter.toLowerCase();
        }
        return match;
    });

    // Add casual interjections
    improved = improved.replace(/\b(important|crucial|key|essential)\b/gi, (match) => {
        if (Math.random() < 0.2) {
            const interjections = [
                `${match} (seriously)`,
                `${match} (trust me)`,
                `${match} (no joke)`,
                `really ${match.toLowerCase()}`
            ];
            return interjections[Math.floor(Math.random() * interjections.length)];
        }
        return match;
    });

    return improved;
}

/**
 * Apply final humanization polish
 */
function applyFinalHumanizationPolish(text, _aiAnalysis) {
    let polished = text;

    // Final aggressive transformation of any remaining formal patterns
    polished = applyUltraAggressiveTransformations(polished);

    // Add final human touches
    polished = polished.replace(/\b(really|very|quite)\s+(good|bad|important|useful)\b/gi, (match, _intensifier, adjective) => {
        const casualIntensifiers = [
            `super ${adjective}`,
            `crazy ${adjective}`,
            `insanely ${adjective}`,
            `ridiculously ${adjective}`,
            `${adjective} as hell`
        ];
        return Math.random() < 0.3 ?
               casualIntensifiers[Math.floor(Math.random() * casualIntensifiers.length)] :
               match;
    });

    // Final error injection for authenticity
    polished = injectHumanLikeErrors(polished, 0.06);

    return polished;
}

/**
 * Nuclear pattern elimination - most aggressive AI pattern destruction
 */
function applyNuclearPatternElimination(text, aiAnalysis) {
    let nuclear = text;

    // Target the highest-risk patterns first
    const criticalPatterns = aiAnalysis.filter(p => p.severity === 'critical');

    criticalPatterns.forEach(pattern => {
        switch (pattern.pattern) {
            case 'formal transitions':
                nuclear = nuclear.replace(/\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|specifically|essentially|ultimately)\b/gi, () => {
                    const casual = ['and', 'plus', 'also', 'so', 'but', 'oh, and', 'by the way', 'anyway'];
                    return casual[Math.floor(Math.random() * casual.length)];
                });
                break;

            case 'robotic qualifiers':
                nuclear = nuclear.replace(/\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized|it is essential to understand)\b/gi, () => {
                    const human = ['here\'s the thing', 'look', 'listen', 'honestly', 'to be real', 'check this out', 'get this'];
                    return human[Math.floor(Math.random() * human.length)];
                });
                break;

            case 'passive voice overuse':
                nuclear = nuclear.replace(/\b(is|are|was|were)\s+(\w+ed)\b/gi, (match, _verb, pastParticiple) => {
                    if (Math.random() < 0.8) { // Very aggressive conversion
                        const activeAlternatives = [
                            `people ${pastParticiple.replace('ed', '')}`,
                            `we ${pastParticiple.replace('ed', '')}`,
                            `they ${pastParticiple.replace('ed', '')}`,
                            `someone ${pastParticiple.replace('ed', 's')}`
                        ];
                        return activeAlternatives[Math.floor(Math.random() * activeAlternatives.length)];
                    }
                    return match;
                });
                break;
        }
    });

    return nuclear;
}

/**
 * Inject advanced casual language markers
 */
function injectAdvancedCasualLanguage(text, intensity = 0.3) {
    let casual = text;

    // Advanced casual markers with contextual placement
    const casualMarkers = {
        sentence_starters: ['Look,', 'Listen,', 'So,', 'Well,', 'Actually,', 'Honestly,', 'To be fair,', 'Here\'s the thing -'],
        intensifiers: ['really', 'pretty', 'quite', 'super', 'crazy', 'insanely', 'ridiculously'],
        connectors: ['and', 'but', 'so', 'plus', 'anyway', 'basically', 'I mean'],
        qualifiers: ['I think', 'probably', 'maybe', 'I guess', 'seems like', 'apparently', 'supposedly']
    };

    // Add sentence starters
    casual = casual.replace(/^([A-Z][^.!?]*[.!?])/gm, (match) => {
        if (Math.random() < intensity) {
            const starter = casualMarkers.sentence_starters[Math.floor(Math.random() * casualMarkers.sentence_starters.length)];
            return starter + ' ' + match.charAt(0).toLowerCase() + match.slice(1);
        }
        return match;
    });

    // Add intensifiers
    casual = casual.replace(/\b(good|bad|important|useful|effective|significant)\b/gi, (match) => {
        if (Math.random() < intensity) {
            const intensifier = casualMarkers.intensifiers[Math.floor(Math.random() * casualMarkers.intensifiers.length)];
            return `${intensifier} ${match.toLowerCase()}`;
        }
        return match;
    });

    // Add qualifiers
    casual = casual.replace(/\b(is|are|will|can|should)\b/g, (match) => {
        if (Math.random() < intensity * 0.5) {
            const qualifier = casualMarkers.qualifiers[Math.floor(Math.random() * casualMarkers.qualifiers.length)];
            return `${qualifier} ${match}`;
        }
        return match;
    });

    return casual;
}

/**
 * Apply final quality enhancement
 */
function applyFinalQualityEnhancement(text, aiAnalysis) {
    let enhanced = text;

    // Final pass to catch any remaining AI patterns
    const remainingPatterns = aiAnalysis.filter(p => p.count > 0);

    if (remainingPatterns.length > 0) {
        console.log(`Final cleanup: ${remainingPatterns.length} patterns remaining`);

        // Apply emergency pattern elimination
        enhanced = enhanced.replace(/\b(comprehensive|extensive|significant|substantial|considerable|numerous|various|multiple|several|diverse)\b/gi, (match) => {
            const simple = {
                'comprehensive': 'complete',
                'extensive': 'big',
                'significant': 'important',
                'substantial': 'large',
                'considerable': 'big',
                'numerous': 'lots of',
                'various': 'different',
                'multiple': 'many',
                'several': 'some',
                'diverse': 'different'
            };
            return simple[match.toLowerCase()] || match;
        });

        // Emergency contraction injection
        enhanced = enhanced.replace(/\b(do not|will not|cannot|should not|would not|could not|it is|that is|there is|we are|they are|you are)\b/gi, (match) => {
            const contractions = {
                'do not': 'don\'t',
                'will not': 'won\'t',
                'cannot': 'can\'t',
                'should not': 'shouldn\'t',
                'would not': 'wouldn\'t',
                'could not': 'couldn\'t',
                'it is': 'it\'s',
                'that is': 'that\'s',
                'there is': 'there\'s',
                'we are': 'we\'re',
                'they are': 'they\'re',
                'you are': 'you\'re'
            };
            return contractions[match.toLowerCase()] || match;
        });
    }

    return enhanced;
}

/**
 * Advanced AI pattern detection system
 * Comprehensive analysis of subtle AI writing signatures
 */
function analyzeRemainingAIPatterns(text) {
    const patterns = {
        // Level 1: Obvious AI patterns (high weight)
        formalTransitions: {
            pattern: /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly|specifically|essentially|ultimately)\b/gi,
            weight: 3,
            description: 'Formal transition words'
        },
        roboticPhrases: {
            pattern: /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized|it is essential to understand|it should be understood)\b/gi,
            weight: 4,
            description: 'Robotic qualifying phrases'
        },
        mechanicalConclusions: {
            pattern: /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly|overall|in essence)\b/gi,
            weight: 3,
            description: 'Mechanical conclusion phrases'
        },

        // Level 2: Subtle AI patterns (medium weight)
        overqualification: {
            pattern: /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various|multiple|several|diverse)\b/gi,
            weight: 2,
            description: 'Over-qualification words'
        },
        passiveOveruse: {
            pattern: /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
            weight: 2,
            description: 'Passive voice overuse'
        },
        technicalJargon: {
            pattern: /\b(utilize|implement|facilitate|optimize|demonstrate|establish|maintain|generate|analyze|evaluate)\b/gi,
            weight: 2,
            description: 'Technical jargon'
        },

        // Level 3: Advanced AI signatures (medium weight)
        perfectGrammar: {
            pattern: /^[A-Z][^.!?]*[.!?]$/gm,
            weight: 1,
            description: 'Perfect sentence structure',
            customAnalysis: (text) => {
                const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
                const perfectSentences = sentences.filter(s => /^[A-Z][^.!?]*$/.test(s.trim()));
                return perfectSentences.length > sentences.length * 0.8 ? perfectSentences.length : 0;
            }
        },
        consistentLength: {
            pattern: null,
            weight: 2,
            description: 'Consistent sentence lengths',
            customAnalysis: (text) => {
                const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
                if (sentences.length < 3) return 0;

                const lengths = sentences.map(s => s.length);
                const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
                const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
                const stdDev = Math.sqrt(variance);

                // Low standard deviation indicates consistent lengths (AI-like)
                return stdDev < 15 ? Math.floor(sentences.length / 3) : 0;
            }
        },

        // Level 4: Lexical patterns (low weight but important)
        repetitiveStructure: {
            pattern: /^[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]/gm,
            weight: 2,
            description: 'Repetitive sentence structures'
        },
        lackOfContractions: {
            pattern: null,
            weight: 1,
            description: 'Lack of contractions',
            customAnalysis: (text) => {
                const contractableWords = text.match(/\b(do not|will not|cannot|should not|would not|could not|it is|that is|there is|we are|they are|you are)\b/gi) || [];
                const contractions = text.match(/\b(don't|won't|can't|shouldn't|wouldn't|couldn't|it's|that's|there's|we're|they're|you're)\b/gi) || [];

                const totalContractable = contractableWords.length + contractions.length;
                if (totalContractable === 0) return 0;

                const contractionRate = contractions.length / totalContractable;
                return contractionRate < 0.3 ? Math.floor(contractableWords.length / 2) : 0;
            }
        },

        // Level 5: Semantic patterns (advanced detection)
        semanticRepetition: {
            pattern: null,
            weight: 1,
            description: 'Semantic repetition patterns',
            customAnalysis: (text) => {
                const words = text.toLowerCase().match(/\b\w{4,}\b/g) || [];
                const wordFreq = {};
                words.forEach(word => {
                    wordFreq[word] = (wordFreq[word] || 0) + 1;
                });

                const overusedWords = Object.entries(wordFreq).filter(([word, freq]) =>
                    freq > 3 && !['that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'].includes(word)
                );

                return overusedWords.length;
            }
        },

        // Level 6: Stylistic uniformity
        stylisticUniformity: {
            pattern: null,
            weight: 1,
            description: 'Stylistic uniformity',
            customAnalysis: (text) => {
                const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
                if (sentences.length < 4) return 0;

                // Check for uniform sentence beginnings
                const beginnings = sentences.map(s => s.trim().split(' ')[0].toLowerCase());
                const uniqueBeginnings = new Set(beginnings);
                const diversityRatio = uniqueBeginnings.size / beginnings.length;

                return diversityRatio < 0.6 ? Math.floor(sentences.length / 4) : 0;
            }
        }
    };

    let riskScore = 0;
    const issues = [];
    const detailedAnalysis = {};

    Object.entries(patterns).forEach(([category, config]) => {
        let matches = [];
        let count = 0;

        if (config.customAnalysis) {
            count = config.customAnalysis(text);
            if (count > 0) {
                matches = [`${count} instances detected`];
            }
        } else if (config.pattern) {
            matches = text.match(config.pattern) || [];
            count = matches.length;
        }

        if (count > 0) {
            const weightedScore = count * config.weight;
            riskScore += weightedScore;

            issues.push({
                category,
                count,
                weight: config.weight,
                weightedScore,
                description: config.description,
                examples: matches.slice(0, 3)
            });
        }

        detailedAnalysis[category] = {
            count,
            weight: config.weight,
            description: config.description
        };
    });

    // Additional sophisticated checks
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;

    // Check for overly long sentences (AI tendency)
    if (avgSentenceLength > 30) {
        riskScore += 3;
        issues.push({
            category: 'longSentences',
            count: 1,
            weight: 3,
            weightedScore: 3,
            description: 'Overly long sentences',
            examples: [`Average sentence length: ${avgSentenceLength.toFixed(1)} characters`]
        });
    }

    // Check for lack of casual language
    const casualMarkers = text.match(/\b(really|pretty|quite|sort of|kind of|basically|actually|honestly|seriously)\b/gi) || [];
    const casualDensity = casualMarkers.length / (text.length / 100); // per 100 characters

    if (casualDensity < 0.5) {
        riskScore += 2;
        issues.push({
            category: 'lackOfCasualLanguage',
            count: 1,
            weight: 2,
            weightedScore: 2,
            description: 'Insufficient casual language markers',
            examples: [`Casual density: ${casualDensity.toFixed(2)} per 100 chars`]
        });
    }

    return {
        riskScore: Math.min(riskScore, 20), // Increased cap for more nuanced scoring
        issues,
        detailedAnalysis,
        recommendations: generateAdvancedRefinementRecommendations(issues),
        textStats: {
            sentenceCount: sentences.length,
            avgSentenceLength: avgSentenceLength.toFixed(1),
            casualDensity: casualDensity.toFixed(2),
            wordCount: text.split(/\s+/).length
        }
    };
}

/**
 * Generate targeted refinement prompt based on AI pattern analysis
 */
function generateRefinementPrompt(text, aiAnalysis, options) {
    const { targetDetection, aggressiveness, originalText } = options;

    const issueDescriptions = aiAnalysis.issues.map(issue =>
        `- ${issue.category}: ${issue.count} instances (e.g., "${issue.examples[0]}")`
    ).join('\n');

    return `You are performing a CRITICAL REFINEMENT PASS to eliminate remaining AI detection patterns. The text below has already been humanized but still contains ${aiAnalysis.riskScore}/10 AI risk factors.

🎯 REFINEMENT MISSION: Achieve ≤${targetDetection}% AI detection by targeting these specific issues:

${issueDescriptions}

🔧 TARGETED REFINEMENT STRATEGIES:
1. ELIMINATE FORMAL TRANSITIONS: Replace with natural thought connectors
2. BREAK REPETITIVE PATTERNS: Vary sentence structures dramatically
3. INJECT HUMAN SPONTANEITY: Add natural digressions and personal touches
4. REDUCE PASSIVE VOICE: Convert to active, engaging language
5. ADD CONVERSATIONAL ELEMENTS: Include natural speech patterns
6. VARY SENTENCE RHYTHM: Mix short punchy sentences with flowing longer ones

🚫 CRITICAL ELIMINATION TARGETS:
- All instances of "furthermore", "moreover", "consequently"
- Robotic phrases like "it is important to note"
- Mechanical conclusions and summaries
- Overuse of qualifying adjectives
- Predictable sentence patterns

✅ PRESERVE COMPLETELY:
- Core message and factual content
- Technical accuracy and data
- Professional credibility
- Logical flow and structure

TEXT TO REFINE:
${text}

ULTRA-HUMANIZED REFINEMENT (≤${targetDetection}% AI detection):`;
}

/**
 * Generate advanced refinement recommendations based on detailed pattern analysis
 */
function generateAdvancedRefinementRecommendations(issues) {
    const recommendations = [];
    const prioritizedIssues = issues.sort((a, b) => b.weightedScore - a.weightedScore);

    prioritizedIssues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                recommendations.push({
                    priority: 'high',
                    action: 'Replace formal transitions with casual connectors',
                    examples: ['furthermore → and', 'consequently → so', 'moreover → plus'],
                    impact: 'Reduces formal tone significantly'
                });
                break;

            case 'roboticPhrases':
                recommendations.push({
                    priority: 'high',
                    action: 'Eliminate robotic qualifying phrases',
                    examples: ['it is important to note → here\'s the thing', 'it should be mentioned → by the way'],
                    impact: 'Removes AI-like hedging language'
                });
                break;

            case 'mechanicalConclusions':
                recommendations.push({
                    priority: 'high',
                    action: 'Use casual conclusion phrases',
                    examples: ['in conclusion → so basically', 'to summarize → bottom line'],
                    impact: 'Makes endings sound more natural'
                });
                break;

            case 'overqualification':
                recommendations.push({
                    priority: 'medium',
                    action: 'Replace over-qualification words with simpler alternatives',
                    examples: ['comprehensive → complete', 'substantial → big', 'numerous → lots of'],
                    impact: 'Reduces academic formality'
                });
                break;

            case 'passiveOveruse':
                recommendations.push({
                    priority: 'medium',
                    action: 'Convert passive voice to active voice',
                    examples: ['is implemented → we implement', 'was created → someone created'],
                    impact: 'Makes text more direct and engaging'
                });
                break;

            case 'technicalJargon':
                recommendations.push({
                    priority: 'medium',
                    action: 'Replace technical jargon with everyday language',
                    examples: ['utilize → use', 'facilitate → help', 'implement → do'],
                    impact: 'Makes text more accessible and human-like'
                });
                break;

            case 'perfectGrammar':
                recommendations.push({
                    priority: 'low',
                    action: 'Introduce minor grammatical variations',
                    examples: ['Add sentence fragments', 'Use casual punctuation'],
                    impact: 'Breaks perfect AI grammar patterns'
                });
                break;

            case 'consistentLength':
                recommendations.push({
                    priority: 'medium',
                    action: 'Vary sentence lengths dramatically',
                    examples: ['Mix short punchy sentences with longer explanations'],
                    impact: 'Creates more natural rhythm'
                });
                break;

            case 'lackOfContractions':
                recommendations.push({
                    priority: 'medium',
                    action: 'Add more contractions and casual language',
                    examples: ['do not → don\'t', 'it is → it\'s', 'we are → we\'re'],
                    impact: 'Increases conversational tone'
                });
                break;

            case 'semanticRepetition':
                recommendations.push({
                    priority: 'low',
                    action: 'Increase lexical diversity',
                    examples: ['Use synonyms for repeated words', 'Vary expressions'],
                    impact: 'Reduces robotic repetition patterns'
                });
                break;

            case 'stylisticUniformity':
                recommendations.push({
                    priority: 'low',
                    action: 'Diversify sentence beginnings and structures',
                    examples: ['Start with "Look,", "Listen,", "So"', 'Mix question and statement forms'],
                    impact: 'Creates more natural variation'
                });
                break;

            case 'longSentences':
                recommendations.push({
                    priority: 'high',
                    action: 'Break long sentences into shorter, punchier ones',
                    examples: ['Split at conjunctions', 'Use dashes for emphasis'],
                    impact: 'Improves readability and naturalness'
                });
                break;

            case 'lackOfCasualLanguage':
                recommendations.push({
                    priority: 'medium',
                    action: 'Inject more casual language markers',
                    examples: ['Add "really", "pretty", "quite"', 'Use "honestly", "actually"'],
                    impact: 'Increases conversational authenticity'
                });
                break;
        }
    });

    return recommendations;
}

/**
 * Generate specific recommendations for refinement (legacy function)
 */
function generateRefinementRecommendations(issues) {
    const recommendations = [];

    issues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                recommendations.push('Replace formal transitions with conversational connectors');
                break;
            case 'roboticPhrases':
                recommendations.push('Eliminate robotic qualifying phrases');
                break;
            case 'mechanicalConclusions':
                recommendations.push('Use natural ending patterns instead of formal conclusions');
                break;
            case 'overqualification':
                recommendations.push('Reduce excessive qualifying adjectives');
                break;
            case 'passiveOveruse':
                recommendations.push('Convert passive voice to active constructions');
                break;
            case 'repetitiveStructure':
                recommendations.push('Vary sentence structures and lengths');
                break;
            case 'longSentences':
                recommendations.push('Break up overly long sentences');
                break;
        }
    });

    return recommendations;
}

/**
 * Perplexity-based validation to ensure natural language flow
 * Lower perplexity = more predictable (AI-like), Higher perplexity = more natural (human-like)
 */
function calculateTextPerplexity(text) {
    // Simplified perplexity calculation based on linguistic patterns
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    if (words.length < 10) return { score: 0, quality: 'insufficient_data' };

    // Calculate word frequency distribution
    const wordFreq = {};
    words.forEach(word => {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
    });

    // Calculate entropy (measure of unpredictability)
    let entropy = 0;
    const totalWords = words.length;

    Object.values(wordFreq).forEach(freq => {
        const probability = freq / totalWords;
        entropy -= probability * Math.log2(probability);
    });

    // Calculate lexical diversity
    const uniqueWords = Object.keys(wordFreq).length;
    const lexicalDiversity = uniqueWords / totalWords;

    // Calculate sentence length variance (natural text has varied sentence lengths)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 5);
    const sentenceLengths = sentences.map(s => s.length);
    const avgSentenceLength = sentenceLengths.reduce((sum, len) => sum + len, 0) / sentenceLengths.length;
    const sentenceVariance = sentenceLengths.reduce((sum, len) => sum + Math.pow(len - avgSentenceLength, 2), 0) / sentenceLengths.length;

    // Calculate bigram surprisal (how unexpected word pairs are)
    const bigrams = [];
    for (let i = 0; i < words.length - 1; i++) {
        bigrams.push(`${words[i]} ${words[i + 1]}`);
    }

    const bigramFreq = {};
    bigrams.forEach(bigram => {
        bigramFreq[bigram] = (bigramFreq[bigram] || 0) + 1;
    });

    const uniqueBigrams = Object.keys(bigramFreq).length;
    const bigramDiversity = uniqueBigrams / bigrams.length;

    // Enhanced composite perplexity score with additional naturalness factors
    const entropyScore = Math.min(entropy / 4, 1) * 20; // Max 20 points
    const diversityScore = lexicalDiversity * 20; // Max 20 points
    const varianceScore = Math.min(Math.sqrt(sentenceVariance) / 20, 1) * 20; // Max 20 points
    const bigramScore = bigramDiversity * 20; // Max 20 points

    // Additional naturalness factors
    const casualMarkers = (text.match(/\b(really|pretty|quite|actually|honestly|well|look|I mean|basically|seriously)\b/gi) || []).length;
    const casualScore = Math.min(casualMarkers / (totalWords / 50), 1) * 10; // Max 10 points for casual language

    const contractions = (text.match(/\b(don't|won't|can't|shouldn't|wouldn't|couldn't|it's|that's|there's|we're|they're|you're)\b/gi) || []).length;
    const contractionScore = Math.min(contractions / (totalWords / 30), 1) * 10; // Max 10 points for contractions

    const perplexityScore = entropyScore + diversityScore + varianceScore + bigramScore + casualScore + contractionScore;

    // Determine quality level
    let quality;
    if (perplexityScore >= 80) quality = 'excellent';
    else if (perplexityScore >= 65) quality = 'good';
    else if (perplexityScore >= 50) quality = 'acceptable';
    else if (perplexityScore >= 35) quality = 'poor';
    else quality = 'very_poor';

    return {
        score: Math.round(perplexityScore),
        quality,
        components: {
            entropy: Math.round(entropyScore),
            lexicalDiversity: Math.round(diversityScore),
            sentenceVariance: Math.round(varianceScore),
            bigramDiversity: Math.round(bigramScore)
        },
        stats: {
            totalWords: totalWords,
            uniqueWords: uniqueWords,
            avgSentenceLength: Math.round(avgSentenceLength),
            sentenceCount: sentences.length
        }
    };
}

/**
 * Validate text quality using perplexity and AI pattern analysis
 */
function validateTextQuality(text, targetDetection = 10) {
    const perplexity = calculateTextPerplexity(text);
    const aiAnalysis = analyzeRemainingAIPatterns(text);

    // Calculate composite quality score
    const perplexityWeight = 0.4;
    const aiPatternWeight = 0.6;

    // Invert AI risk score (lower risk = higher quality)
    const aiQualityScore = Math.max(0, 100 - (aiAnalysis.riskScore * 5));

    const compositeScore = (perplexity.score * perplexityWeight) + (aiQualityScore * aiPatternWeight);

    // Enhanced quality standards for ≤10% AI detection achievement
    const strictPerplexityThreshold = targetDetection <= 5 ? 70 : 60; // Higher threshold for commercial grade
    const strictAIRiskThreshold = targetDetection <= 5 ? 1 : 2; // Much stricter AI risk tolerance
    const strictCompositeThreshold = targetDetection <= 5 ? 75 : 65; // Higher composite score requirement

    const meetsPerplexityStandard = perplexity.score >= strictPerplexityThreshold;
    const meetsAIDetectionTarget = aiAnalysis.riskScore <= strictAIRiskThreshold;
    const meetsCompositeStandard = compositeScore >= strictCompositeThreshold;

    const overallQuality = meetsPerplexityStandard && meetsAIDetectionTarget && meetsCompositeStandard;

    return {
        overallQuality,
        compositeScore: Math.round(compositeScore),
        perplexity,
        aiAnalysis,
        recommendations: overallQuality ? [] : generateQualityImprovementRecommendations(perplexity, aiAnalysis),
        meetsStandards: {
            perplexity: meetsPerplexityStandard,
            aiDetection: meetsAIDetectionTarget,
            composite: meetsCompositeStandard
        }
    };
}

/**
 * Generate recommendations for improving text quality
 */
function generateQualityImprovementRecommendations(perplexity, aiAnalysis) {
    const recommendations = [];

    if (perplexity.score < 50) {
        if (perplexity.components.entropy < 15) {
            recommendations.push('Increase vocabulary diversity - use more varied word choices');
        }
        if (perplexity.components.lexicalDiversity < 15) {
            recommendations.push('Reduce word repetition - find synonyms for frequently used terms');
        }
        if (perplexity.components.sentenceVariance < 15) {
            recommendations.push('Vary sentence lengths more dramatically - mix short and long sentences');
        }
        if (perplexity.components.bigramDiversity < 15) {
            recommendations.push('Use more varied word combinations and phrases');
        }
    }

    if (aiAnalysis.riskScore > 5) {
        recommendations.push('Apply more aggressive AI pattern elimination');
        recommendations.push('Increase casual language markers and contractions');
        recommendations.push('Break up formal sentence structures');
    }

    return recommendations;
}

/**
 * Assess readability of humanized text
 */
function assessReadability(text) {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 5);
    const words = text.split(/\s+/).filter(w => w.length > 0);
    const syllables = words.reduce((count, word) => count + countSyllables(word), 0);

    // Flesch Reading Ease approximation
    const avgSentenceLength = words.length / sentences.length;
    const avgSyllablesPerWord = syllables / words.length;
    const fleschScore = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord);

    // Convert to 0-100 scale where higher is better
    const readabilityScore = Math.max(0, Math.min(100, fleschScore));

    let level;
    if (readabilityScore >= 90) level = 'very_easy';
    else if (readabilityScore >= 80) level = 'easy';
    else if (readabilityScore >= 70) level = 'fairly_easy';
    else if (readabilityScore >= 60) level = 'standard';
    else if (readabilityScore >= 50) level = 'fairly_difficult';
    else level = 'difficult';

    return {
        score: Math.round(readabilityScore),
        level,
        avgSentenceLength: Math.round(avgSentenceLength),
        avgSyllablesPerWord: avgSyllablesPerWord.toFixed(2)
    };
}

/**
 * Simple syllable counting function
 */
function countSyllables(word) {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    word = word.replace(/(?:[^laeiouy]es|ed|[^laeiouy]e)$/, '');
    word = word.replace(/^y/, '');
    const matches = word.match(/[aeiouy]{1,2}/g);
    return matches ? matches.length : 1;
}

/**
 * Assess transformation quality and rate
 */
function assessTransformationQuality(originalText, transformedText) {
    const originalWords = originalText.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    const transformedWords = transformedText.toLowerCase().split(/\s+/).filter(w => w.length > 2);

    // Calculate word-level changes
    const changedWords = originalWords.filter((word, index) => {
        const transformedWord = transformedWords[index];
        return transformedWord && word !== transformedWord;
    });

    const transformationRate = (changedWords.length / originalWords.length) * 100;

    // Assess semantic preservation
    const commonWords = originalWords.filter(word => transformedWords.includes(word));
    const semanticPreservation = (commonWords.length / originalWords.length) * 100;

    // Length change assessment
    const lengthChange = ((transformedText.length - originalText.length) / originalText.length) * 100;

    return {
        rate: Math.round(transformationRate),
        semanticPreservation: Math.round(semanticPreservation),
        lengthChange: Math.round(lengthChange),
        quality: transformationRate >= 60 && semanticPreservation >= 40 ? 'good' :
                transformationRate >= 40 && semanticPreservation >= 60 ? 'moderate' : 'poor'
    };
}

/**
 * Assess if professional tone is maintained
 */
function assessProfessionalTone(text) {
    // Check for excessive casual language that might hurt professionalism
    const excessivelyCasual = [
        /\b(gonna|wanna|gotta|outta|kinda|sorta)\b/gi,
        /\b(yeah|yep|nah|nope)\b/gi,
        /\b(super|crazy|insane|ridiculous)\s+(good|bad|important|useful)/gi,
        /\b(as hell|like crazy|insanely)\b/gi
    ];

    let casualCount = 0;
    excessivelyCasual.forEach(pattern => {
        const matches = text.match(pattern) || [];
        casualCount += matches.length;
    });

    const words = text.split(/\s+/).length;
    const casualDensity = (casualCount / words) * 100;

    // Check for maintained professional elements
    const professionalElements = [
        /\b(analysis|research|study|findings|results|data|evidence|methodology)\b/gi,
        /\b(important|significant|effective|efficient|comprehensive|substantial)\b/gi,
        /\b(however|therefore|furthermore|moreover|consequently)\b/gi
    ];

    let professionalCount = 0;
    professionalElements.forEach(pattern => {
        const matches = text.match(pattern) || [];
        professionalCount += matches.length;
    });

    const professionalDensity = (professionalCount / words) * 100;

    // Professional tone is maintained if casual density is low and professional elements remain
    const maintained = casualDensity < 2 && professionalDensity > 0.5;

    return {
        maintained,
        casualDensity: casualDensity.toFixed(2),
        professionalDensity: professionalDensity.toFixed(2),
        assessment: maintained ? 'professional' : casualDensity > 3 ? 'too_casual' : 'acceptable'
    };
}

/**
 * Enhanced validation with more sophisticated AI pattern detection
 */
async function validateDetectionTarget(text, targetDetection) {
    // Enhanced heuristic validation - in production, this would call an AI detection API

    const aiPatterns = [
        { pattern: /\b(furthermore|moreover|consequently|therefore|thus|hence)\b/gi, weight: 3 },
        { pattern: /\b(it is important to note|it should be noted|it is worth mentioning)\b/gi, weight: 4 },
        { pattern: /\b(in conclusion|to summarize|in summary)\b/gi, weight: 3 },
        { pattern: /\b(comprehensive|extensive|significant|substantial|considerable)\b/gi, weight: 2 },
        { pattern: /\b(numerous|various|multiple|several)\s+\w+/gi, weight: 1 },
        { pattern: /\b(is|are|was|were)\s+\w+ed\b/gi, weight: 1 }
    ];

    let weightedScore = 0;
    let totalMatches = 0;

    aiPatterns.forEach(({ pattern, weight }) => {
        const matches = text.match(pattern) || [];
        weightedScore += matches.length * weight;
        totalMatches += matches.length;
    });

    // Calculate estimated detection percentage
    const textLength = text.length;
    const density = (weightedScore / textLength) * 1000; // Patterns per 1000 characters
    const estimatedDetection = Math.min(density * 8, 95); // Adjusted multiplier

    console.log(`Detection validation: ${totalMatches} patterns, density: ${density.toFixed(2)}, estimated: ${estimatedDetection.toFixed(1)}%`);

    return estimatedDetection <= targetDetection;
}

/**
 * Performance-optimized NLTK-inspired text processing with advanced caching and parallel processing
 */
async function processTextWithNLTKApproachOptimized(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        useAdvancedSynonyms = true,
        performanceConfig = PERFORMANCE_TIERS.free,
        userTier = 'free'
    } = options;

    // Use optimized regex patterns for premium/admin users
    const sentenceRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.sentences : /[^.!?]*[.!?]+\s*|[^.!?]+$/g;
    const tokenRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.tokens : /\w+|[^\w\s]+/g;

    // Preserve newlines with placeholder
    const newlinePlaceholder = "庄周";
    const textWithPlaceholders = text.replace(/\n/g, newlinePlaceholder);

    // Split into sentences using optimized regex
    const sentences = textWithPlaceholders.match(sentenceRegex) || [textWithPlaceholders];

    // Use parallel processing for premium/admin users with larger texts
    if (performanceConfig.parallelProcessing && sentences.length > 3) {
        return await processTextParallel(sentences, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex,
            newlinePlaceholder
        });
    } else {
        return await processTextSequential(sentences, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex,
            newlinePlaceholder
        });
    }
}

/**
 * Parallel processing for premium/admin users - processes sentences concurrently
 */
async function processTextParallel(sentences, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex, newlinePlaceholder } = options;

    // Process sentences in parallel batches
    const batchSize = performanceConfig.batchSize;
    const batches = [];

    for (let i = 0; i < sentences.length; i += batchSize) {
        batches.push(sentences.slice(i, i + batchSize));
    }

    const processedBatches = await Promise.all(batches.map(async (batch) => {
        return await Promise.all(batch.map(async (sentence) => {
            return await processSingleSentenceOptimized(sentence, {
                aggressiveness,
                useAdvancedSynonyms,
                performanceConfig,
                userTier,
                tokenRegex
            });
        }));
    }));

    // Flatten batches and join results
    const humanizedSentences = processedBatches.flat();
    let result = humanizedSentences.join('');

    // Final cleanup and restore newlines
    result = cleanSymbolsOptimized(result, performanceConfig);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Sequential processing for free users - processes sentences one by one
 */
async function processTextSequential(sentences, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex, newlinePlaceholder } = options;

    const humanizedSentences = [];

    for (const sentence of sentences) {
        const processedSentence = await processSingleSentenceOptimized(sentence, {
            aggressiveness,
            useAdvancedSynonyms,
            performanceConfig,
            userTier,
            tokenRegex
        });
        humanizedSentences.push(processedSentence);
    }

    // Join results and cleanup
    let result = humanizedSentences.join('');
    result = cleanSymbolsOptimized(result, performanceConfig);
    result = result.replace(new RegExp(newlinePlaceholder, 'g'), '\n');

    return result.trim();
}

/**
 * Optimized single sentence processing with caching and performance enhancements
 */
async function processSingleSentenceOptimized(sentence, options) {
    const { aggressiveness, useAdvancedSynonyms, performanceConfig, userTier, tokenRegex } = options;

    if (!sentence.trim()) {
        return sentence;
    }

    // Check sentence cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cacheKey = `sentence_${sentence}_${aggressiveness}_${userTier}`;
        const cached = optimizedCaches.sentence.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    // Tokenize sentence using optimized regex
    const tokens = sentence.match(tokenRegex) || [];

    // Get POS tags with caching
    const posTags = getPOSTagsOptimized(sentence, performanceConfig);

    // Process tokens with optimized replacement
    const humanizedTokens = [];
    let wordIndex = 0;

    for (const token of tokens) {
        if (optimizedRegexPatterns.wordToken.test(token)) {
            const posTag = posTags[wordIndex] || { word: token, pos: 'NN' };
            const humanizedWord = await replaceWordWithPOSOptimized(
                token,
                posTag.pos,
                aggressiveness,
                useAdvancedSynonyms,
                performanceConfig
            );
            humanizedTokens.push(humanizedWord);
            wordIndex++;
        } else {
            humanizedTokens.push(token);
        }
    }

    // Reconstruct sentence with optimized spacing
    let humanizedSentence = reconstructSentenceOptimized(humanizedTokens, performanceConfig);

    // Cache result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cacheKey = `sentence_${sentence}_${aggressiveness}_${userTier}`;
        optimizedCaches.sentence.set(cacheKey, humanizedSentence);
    }

    return humanizedSentence;
}

/**
 * Legacy function for backward compatibility
 */
async function processTextWithNLTKApproach(text, options = {}) {
    return await processTextWithNLTKApproachOptimized(text, {
        ...options,
        performanceConfig: PERFORMANCE_TIERS.free,
        userTier: 'free'
    });
}

/**
 * Optimized POS tagging with caching and performance enhancements
 */
function getPOSTagsOptimized(sentence, performanceConfig) {
    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.pos.get(sentence);
        if (cached) {
            return cached;
        }
    }

    // Use optimized regex for premium/admin users
    const wordRegex = performanceConfig.useOptimizedRegex ?
        optimizedRegexPatterns.words : /\w+/g;

    const words = sentence.match(wordRegex) || [];
    const tags = [];

    for (const word of words) {
        const pos = determinePOSTagOptimized(word.toLowerCase(), performanceConfig);
        tags.push({ word, pos });
    }

    // Cache result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.pos.set(sentence, tags);
    }

    return tags;
}

/**
 * Optimized word replacement with advanced caching and performance tiers
 */
async function replaceWordWithPOSOptimized(word, posTag, aggressiveness, useAdvancedSynonyms, performanceConfig) {
    // Skip very short words or common words using optimized lookup
    if (word.length <= 2 || isCommonWordOptimized(word, performanceConfig)) {
        return word;
    }

    const cacheKey = `${word.toLowerCase()}_${posTag}_${aggressiveness}`;

    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.word.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    // Determine replacement probability based on POS tag
    let replacementProbability = 0.3;

    if (posTag.startsWith('JJ')) {
        replacementProbability = 0.6 * aggressiveness;
    } else if (posTag.startsWith('RB')) {
        replacementProbability = 0.5 * aggressiveness;
    } else if (posTag.startsWith('VB')) {
        replacementProbability = 0.4 * aggressiveness;
    } else {
        replacementProbability = 0.2 * aggressiveness;
    }

    // Random decision to replace
    if (Math.random() > replacementProbability) {
        if (performanceConfig.cacheEnabled) {
            optimizedCaches.word.set(cacheKey, word);
        }
        return word;
    }

    // Get synonyms with optimized lookup
    const synonyms = getSynonymsByPOSOptimized(word.toLowerCase(), posTag, performanceConfig);

    if (synonyms.length === 0) {
        if (performanceConfig.cacheEnabled) {
            optimizedCaches.word.set(cacheKey, word);
        }
        return word;
    }

    // Choose synonym with optimized selection
    const chosenSynonym = selectBestSynonymOptimized(synonyms, word, performanceConfig);
    const result = preserveCapitalization(word, chosenSynonym);

    // Cache the result
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.word.set(cacheKey, result);
    }

    return result;
}

/**
 * Simple POS tagging for JavaScript (inspired by NLTK approach)
 */
function getPOSTags(sentence) {
    const words = sentence.match(/\w+/g) || [];
    const tags = [];

    for (const word of words) {
        const pos = determinePOSTag(word.toLowerCase());
        tags.push({ word, pos });
    }

    return tags;
}

/**
 * Optimized POS tag determination with pre-compiled patterns and caching
 */
function determinePOSTagOptimized(word, performanceConfig) {
    // Use optimized regex patterns for premium/admin users
    if (performanceConfig.useOptimizedRegex) {
        // Adverb patterns (RB) - check first as they're more specific
        if (optimizedRegexPatterns.adverb.test(word)) return 'RB';

        // Adjective patterns (JJ)
        if (optimizedRegexPatterns.adjective.test(word)) return 'JJ';

        // Verb patterns (VB)
        if (optimizedRegexPatterns.verb.test(word)) return 'VB';

        // Common adverbs (pre-computed set for O(1) lookup)
        if (performanceConfig.usePrecomputedSets && commonAdverbsSet.has(word)) return 'RB';

        // Common adjectives (pre-computed set for O(1) lookup)
        if (performanceConfig.usePrecomputedSets && commonAdjectivesSet.has(word)) return 'JJ';
    }

    // Fallback to basic pattern matching
    return determinePOSTag(word);
}

/**
 * Optimized common word checking with pre-computed sets
 */
function isCommonWordOptimized(word, performanceConfig) {
    if (performanceConfig.usePrecomputedSets) {
        return commonWordsSet.has(word.toLowerCase());
    }
    return isCommonWord(word);
}

/**
 * Optimized synonym selection with performance-based algorithms
 */
function selectBestSynonymOptimized(synonyms, originalWord, performanceConfig) {
    if (synonyms.length === 0) return originalWord;

    // For admin users, use more sophisticated selection
    if (performanceConfig.algorithmComplexity === 'maximum') {
        // Select based on word length similarity and frequency
        const targetLength = originalWord.length;
        const scored = synonyms.map(syn => ({
            word: syn,
            score: Math.abs(syn.length - targetLength) + Math.random() * 0.5
        }));
        scored.sort((a, b) => a.score - b.score);
        return scored[0].word;
    }

    // For premium users, select from top 3 most common
    if (performanceConfig.algorithmComplexity === 'optimized') {
        const topSynonyms = synonyms.slice(0, Math.min(3, synonyms.length));
        return topSynonyms[Math.floor(Math.random() * topSynonyms.length)];
    }

    // For free users, simple random selection
    return synonyms[Math.floor(Math.random() * synonyms.length)];
}

/**
 * Optimized sentence reconstruction with performance enhancements
 */
function reconstructSentenceOptimized(tokens, performanceConfig) {
    if (performanceConfig.algorithmComplexity === 'maximum') {
        // Advanced reconstruction with better spacing logic
        let result = '';
        for (let i = 0; i < tokens.length; i++) {
            const token = tokens[i];
            const prevToken = i > 0 ? tokens[i - 1] : null;

            // Smart spacing based on token types
            if (i > 0 && optimizedRegexPatterns.wordStart.test(token) &&
                prevToken && optimizedRegexPatterns.wordEnd.test(prevToken)) {
                result += ' ';
            }
            result += token;
        }
        return result;
    }

    // Basic reconstruction for free/premium users
    let result = '';
    for (let i = 0; i < tokens.length; i++) {
        const token = tokens[i];
        if (i > 0 && /^\w/.test(token) && /\w$/.test(tokens[i-1])) {
            result += ' ';
        }
        result += token;
    }
    return result;
}

/**
 * Optimized symbol cleaning with performance tiers
 */
function cleanSymbolsOptimized(text, performanceConfig) {
    if (performanceConfig.algorithmComplexity === 'maximum') {
        // Advanced cleaning with multiple passes
        let cleaned = text.replace(/\s+/g, ' ');
        cleaned = cleaned.replace(/\s+([.!?,:;])/g, '$1');
        cleaned = cleaned.replace(/([.!?])\s*([A-Z])/g, '$1 $2');
        cleaned = cleaned.replace(/([.!?])([A-Z])/g, '$1 $2');
        cleaned = cleaned.replace(/,([A-Za-z])/g, ', $1');
        cleaned = cleaned.replace(/([.!?])\s{2,}/g, '$1 ');
        return cleaned.trim();
    }

    // Basic cleaning for free/premium users
    return cleanSymbols(text);
}

// Pre-computed sets for O(1) lookups (premium/admin optimization)
const commonAdverbsSet = new Set([
    'very', 'really', 'quite', 'rather', 'extremely', 'incredibly', 'remarkably', 'exceptionally',
    'particularly', 'especially', 'quickly', 'rapidly', 'swiftly', 'slowly', 'gradually',
    'often', 'frequently', 'usually', 'normally', 'typically', 'generally', 'commonly',
    'sometimes', 'occasionally', 'rarely', 'seldom', 'never', 'always', 'constantly',
    'clearly', 'obviously', 'evidently', 'apparently', 'definitely', 'certainly', 'probably'
]);

const commonAdjectivesSet = new Set([
    'good', 'bad', 'great', 'small', 'large', 'big', 'new', 'old', 'high', 'low',
    'important', 'different', 'possible', 'available', 'necessary', 'special', 'certain',
    'easy', 'hard', 'difficult', 'simple', 'complex', 'fast', 'slow', 'quick', 'strong',
    'weak', 'beautiful', 'ugly', 'happy', 'sad', 'young', 'early', 'late', 'recent'
]);

/**
 * Determine POS tag for a word using pattern matching
 */
function determinePOSTag(word) {
    // Adjective patterns (JJ)
    if (word.match(/^(very|quite|rather|extremely|highly|incredibly|amazingly|particularly|especially|remarkably)$/)) return 'RB';
    if (word.match(/(ful|less|ous|ive|able|ible|al|ic|ed|ing)$/)) return 'JJ';
    if (word.match(/^(good|bad|great|small|large|big|new|old|high|low|long|short|important|different|possible|available|necessary|special|certain|clear|simple|easy|difficult|hard|strong|weak|beautiful|ugly|happy|sad|young|early|late|recent|current|future|past|present|real|true|false|right|wrong|correct|proper|main|major|minor|basic|general|specific|particular|common|rare|unique|similar|different|various|several|many|few|much|little|more|most|less|least|best|worst|better|worse|first|last|next|previous|final|initial|original|natural|artificial|public|private|personal|professional|social|political|economic|financial|legal|medical|technical|scientific|educational|cultural|historical|modern|traditional|contemporary|ancient|recent|future|international|national|local|regional|global|worldwide|domestic|foreign|external|internal|upper|lower|middle|central|northern|southern|eastern|western|left|right|front|back|top|bottom|inside|outside|above|below|near|far|close|distant|direct|indirect|positive|negative|active|passive|open|closed|free|busy|full|empty|complete|incomplete|perfect|imperfect|normal|abnormal|regular|irregular|standard|special|ordinary|extraordinary|typical|unusual|common|rare|popular|unpopular|famous|unknown|successful|unsuccessful|effective|ineffective|efficient|inefficient|useful|useless|helpful|harmful|safe|dangerous|secure|insecure|stable|unstable|reliable|unreliable|accurate|inaccurate|exact|approximate|precise|vague|clear|unclear|obvious|hidden|visible|invisible|bright|dark|light|heavy|soft|hard|smooth|rough|hot|cold|warm|cool|wet|dry|clean|dirty|fresh|old|sharp|dull|loud|quiet|fast|slow|quick|gradual|sudden|immediate|delayed|temporary|permanent|brief|long|short|tall|wide|narrow|thick|thin|deep|shallow|rich|poor|expensive|cheap|valuable|worthless)$/)) return 'JJ';

    // Adverb patterns (RB)
    if (word.match(/(ly|ward|wise)$/)) return 'RB';
    if (word.match(/^(very|quite|rather|really|actually|basically|generally|usually|normally|typically|commonly|frequently|often|sometimes|occasionally|rarely|seldom|never|always|constantly|continuously|regularly|irregularly|immediately|instantly|quickly|slowly|gradually|suddenly|recently|currently|previously|formerly|originally|initially|finally|eventually|ultimately|definitely|certainly|probably|possibly|maybe|perhaps|obviously|clearly|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|absolutely|completely|totally|entirely|fully|partly|partially|mostly|mainly|primarily|chiefly|largely|generally|specifically|particularly|especially|notably|remarkably|significantly|considerably|substantially|slightly|somewhat|fairly|pretty|quite|rather|extremely|highly|incredibly|amazingly|surprisingly|interestingly|fortunately|unfortunately|hopefully|thankfully|regrettably|sadly|happily|luckily|unluckily|naturally|obviously|clearly|evidently|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|certainly|definitely|probably|possibly|maybe|perhaps|here|there|everywhere|anywhere|somewhere|nowhere|inside|outside|above|below|beneath|under|over|through|across|around|along|beside|behind|before|after|during|within|without|beyond|towards|away|forward|backward|upward|downward|inward|outward|homeward|eastward|westward|northward|southward|today|tomorrow|yesterday|now|then|soon|later|earlier|before|after|already|still|yet|again|once|twice|often|always|never|sometimes|usually|normally|typically|generally|specifically|particularly|especially|mainly|mostly|largely|primarily|chiefly|basically|essentially|fundamentally|originally|initially|finally|eventually|ultimately|immediately|instantly|quickly|slowly|gradually|suddenly|recently|currently|previously|formerly|definitely|certainly|probably|possibly|maybe|perhaps|obviously|clearly|apparently|seemingly|supposedly|allegedly|reportedly|presumably|undoubtedly|surely|absolutely|completely|totally|entirely|fully|partly|partially|slightly|somewhat|fairly|pretty|quite|rather|extremely|highly|incredibly|amazingly|surprisingly|interestingly|fortunately|unfortunately|hopefully|thankfully|regrettably|sadly|happily|luckily|unluckily|naturally)$/)) return 'RB';

    // Verb patterns (VB)
    if (word.match(/(ed|ing|s)$/)) return 'VB';
    if (word.match(/^(is|are|was|were|be|been|being|have|has|had|having|do|does|did|doing|will|would|could|should|might|may|can|must|shall|ought|need|dare|used)$/)) return 'VB';

    // Noun patterns (NN) - default
    return 'NN';
}

/**
 * Replace word based on POS tag (inspired by NLTK approach)
 */
async function replaceWordWithPOS(word, posTag, aggressiveness, useAdvancedSynonyms) {
    // Skip very short words or common words
    if (word.length <= 2 || isCommonWord(word)) {
        return word;
    }

    // Determine replacement probability based on POS tag
    let replacementProbability = 0.3; // Base probability

    if (posTag.startsWith('JJ')) {
        // Adjectives - higher replacement rate
        replacementProbability = 0.6 * aggressiveness;
    } else if (posTag.startsWith('RB')) {
        // Adverbs - higher replacement rate
        replacementProbability = 0.5 * aggressiveness;
    } else if (posTag.startsWith('VB')) {
        // Verbs - moderate replacement rate
        replacementProbability = 0.4 * aggressiveness;
    } else {
        // Nouns and others - lower replacement rate
        replacementProbability = 0.2 * aggressiveness;
    }

    // Random decision to replace
    if (Math.random() > replacementProbability) {
        return word;
    }

    // Get synonyms based on POS tag
    const synonyms = getSynonymsByPOS(word.toLowerCase(), posTag);

    if (synonyms.length === 0) {
        return word;
    }

    // Choose most appropriate synonym (frequency-based selection)
    const chosenSynonym = selectBestSynonym(synonyms, word);

    // Preserve original capitalization
    return preserveCapitalization(word, chosenSynonym);
}

/**
 * Check if word is too common to replace
 */
function isCommonWord(word) {
    const commonWords = new Set([
        'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
        'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after', 'above', 'below',
        'between', 'among', 'under', 'over', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
        'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may',
        'might', 'must', 'can', 'shall', 'this', 'that', 'these', 'those', 'i', 'you', 'he',
        'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his',
        'her', 'its', 'our', 'their'
    ]);

    return commonWords.has(word.toLowerCase());
}

/**
 * Clean symbols and spacing (similar to Python clean_symbols function)
 */
function cleanSymbols(text) {
    // Remove extra spaces
    let cleaned = text.replace(/\s+/g, ' ');

    // Fix spacing around punctuation - be more careful to preserve word spacing
    cleaned = cleaned.replace(/\s+([.!?,:;])/g, '$1'); // Remove space before punctuation
    cleaned = cleaned.replace(/([.!?])\s*([A-Z])/g, '$1 $2'); // Ensure space after sentence endings
    cleaned = cleaned.replace(/([.!?])([A-Z])/g, '$1 $2'); // Fix missing space after sentence endings
    cleaned = cleaned.replace(/,([A-Za-z])/g, ', $1'); // Fix missing space after commas

    return cleaned.trim();
}

/**
 * Check if advanced LLM service is available
 */
export function isAdvancedLLMAvailable() {
    // Check if at least one API key is configured
    const availableKeys = [
        'FIREWORKS_API_KEY',
        'NOVITA_API_KEY',
        'OPENROUTER_API_KEY',
        'GROQ_API_KEY'
    ].filter(key => process.env[key]);

    return availableKeys.length > 0;
}

/**
 * Optimized synonym lookup with caching and performance enhancements
 */
function getSynonymsByPOSOptimized(word, posTag, performanceConfig) {
    const cacheKey = `${word}_${posTag}`;

    // Use cache for premium/admin users
    if (performanceConfig.cacheEnabled) {
        const cached = optimizedCaches.synonym.get(cacheKey);
        if (cached) {
            return cached;
        }
    }

    const synonyms = getSynonymsByPOS(word, posTag);

    // Cache the result for premium/admin users
    if (performanceConfig.cacheEnabled) {
        optimizedCaches.synonym.set(cacheKey, synonyms);
    }

    return synonyms;
}

/**
 * Get synonyms based on POS tag (inspired by NLTK WordNet approach)
 */
function getSynonymsByPOS(word, posTag) {
    // Comprehensive synonym database organized by POS tags
    const synonymDatabase = {
        // Adjectives (JJ)
        'JJ': {
            'good': ['excellent', 'great', 'fine', 'wonderful', 'superb', 'outstanding', 'remarkable', 'fantastic', 'terrific', 'awesome'],
            'bad': ['terrible', 'awful', 'horrible', 'dreadful', 'poor', 'lousy', 'rotten', 'nasty', 'unpleasant', 'disappointing'],
            'big': ['large', 'huge', 'enormous', 'massive', 'gigantic', 'vast', 'immense', 'colossal', 'tremendous', 'substantial'],
            'small': ['tiny', 'little', 'minute', 'compact', 'petite', 'miniature', 'microscopic', 'diminutive', 'modest', 'limited'],
            'important': ['crucial', 'vital', 'essential', 'significant', 'critical', 'key', 'major', 'fundamental', 'primary', 'central'],
            'different': ['distinct', 'unique', 'separate', 'various', 'diverse', 'alternative', 'contrasting', 'dissimilar', 'varied', 'other'],
            'new': ['fresh', 'recent', 'modern', 'latest', 'current', 'novel', 'innovative', 'contemporary', 'updated', 'brand-new'],
            'old': ['ancient', 'aged', 'elderly', 'vintage', 'antique', 'mature', 'seasoned', 'traditional', 'classic', 'outdated'],
            'high': ['elevated', 'tall', 'lofty', 'towering', 'superior', 'advanced', 'intense', 'extreme', 'peak', 'maximum'],
            'low': ['reduced', 'minimal', 'decreased', 'inferior', 'bottom', 'shallow', 'minor', 'slight', 'modest', 'limited'],
            'easy': ['simple', 'effortless', 'straightforward', 'uncomplicated', 'basic', 'elementary', 'smooth', 'manageable', 'clear', 'plain'],
            'hard': ['difficult', 'challenging', 'tough', 'complex', 'complicated', 'demanding', 'strenuous', 'arduous', 'rigorous', 'intense'],
            'fast': ['quick', 'rapid', 'swift', 'speedy', 'hasty', 'brisk', 'prompt', 'immediate', 'instant', 'accelerated'],
            'slow': ['gradual', 'leisurely', 'sluggish', 'delayed', 'unhurried', 'steady', 'measured', 'deliberate', 'prolonged', 'extended']
        },

        // Adverbs (RB)
        'RB': {
            'very': ['extremely', 'incredibly', 'remarkably', 'exceptionally', 'particularly', 'especially', 'quite', 'rather', 'pretty', 'fairly'],
            'really': ['truly', 'genuinely', 'actually', 'honestly', 'seriously', 'definitely', 'certainly', 'absolutely', 'completely', 'totally'],
            'quickly': ['rapidly', 'swiftly', 'speedily', 'hastily', 'promptly', 'immediately', 'instantly', 'briskly', 'efficiently', 'urgently'],
            'slowly': ['gradually', 'leisurely', 'steadily', 'carefully', 'deliberately', 'methodically', 'patiently', 'gently', 'cautiously', 'unhurriedly'],
            'often': ['frequently', 'regularly', 'commonly', 'typically', 'usually', 'generally', 'repeatedly', 'consistently', 'habitually', 'routinely'],
            'sometimes': ['occasionally', 'periodically', 'intermittently', 'sporadically', 'now and then', 'from time to time', 'once in a while', 'at times', 'every so often', 'infrequently'],
            'always': ['constantly', 'continuously', 'perpetually', 'consistently', 'invariably', 'forever', 'eternally', 'endlessly', 'permanently', 'unfailingly'],
            'never': ['not ever', 'at no time', 'under no circumstances', 'not once', 'not at all', 'by no means', 'absolutely not', 'certainly not', 'definitely not', 'positively not'],
            'clearly': ['obviously', 'evidently', 'apparently', 'plainly', 'distinctly', 'unmistakably', 'undoubtedly', 'certainly', 'definitely', 'undeniably'],
            'probably': ['likely', 'presumably', 'possibly', 'conceivably', 'potentially', 'maybe', 'perhaps', 'chances are', 'in all likelihood', 'most likely']
        },

        // Verbs (VB)
        'VB': {
            'show': ['demonstrate', 'display', 'exhibit', 'reveal', 'present', 'indicate', 'illustrate', 'manifest', 'expose', 'prove'],
            'make': ['create', 'produce', 'generate', 'construct', 'build', 'manufacture', 'form', 'develop', 'establish', 'cause'],
            'get': ['obtain', 'acquire', 'receive', 'gain', 'secure', 'achieve', 'attain', 'procure', 'fetch', 'retrieve'],
            'give': ['provide', 'offer', 'supply', 'deliver', 'present', 'grant', 'bestow', 'contribute', 'donate', 'hand over'],
            'use': ['utilize', 'employ', 'apply', 'implement', 'operate', 'handle', 'manipulate', 'exercise', 'practice', 'exploit'],
            'find': ['discover', 'locate', 'identify', 'detect', 'uncover', 'spot', 'encounter', 'come across', 'stumble upon', 'determine'],
            'think': ['believe', 'consider', 'suppose', 'assume', 'imagine', 'reckon', 'feel', 'suspect', 'presume', 'contemplate'],
            'know': ['understand', 'realize', 'recognize', 'comprehend', 'grasp', 'perceive', 'acknowledge', 'be aware of', 'be familiar with', 'appreciate'],
            'help': ['assist', 'aid', 'support', 'facilitate', 'contribute', 'serve', 'benefit', 'back', 'cooperate', 'collaborate'],
            'work': ['function', 'operate', 'perform', 'labor', 'toil', 'strive', 'endeavor', 'effort', 'serve', 'act']
        },

        // Nouns (NN)
        'NN': {
            'thing': ['item', 'object', 'element', 'matter', 'subject', 'issue', 'aspect', 'factor', 'component', 'entity'],
            'way': ['method', 'approach', 'manner', 'technique', 'strategy', 'procedure', 'process', 'system', 'means', 'route'],
            'time': ['period', 'moment', 'instance', 'occasion', 'duration', 'interval', 'phase', 'stage', 'era', 'epoch'],
            'person': ['individual', 'human', 'being', 'character', 'figure', 'someone', 'citizen', 'resident', 'inhabitant', 'soul'],
            'place': ['location', 'spot', 'site', 'position', 'area', 'region', 'zone', 'venue', 'destination', 'setting'],
            'problem': ['issue', 'difficulty', 'challenge', 'obstacle', 'complication', 'dilemma', 'trouble', 'concern', 'matter', 'situation'],
            'idea': ['concept', 'notion', 'thought', 'suggestion', 'proposal', 'plan', 'scheme', 'theory', 'hypothesis', 'perspective'],
            'system': ['structure', 'framework', 'organization', 'arrangement', 'setup', 'network', 'mechanism', 'process', 'method', 'approach'],
            'information': ['data', 'details', 'facts', 'knowledge', 'intelligence', 'material', 'content', 'evidence', 'documentation', 'statistics'],
            'example': ['instance', 'case', 'illustration', 'sample', 'specimen', 'model', 'demonstration', 'representation', 'prototype', 'pattern']
        }
    };

    // Get POS category
    let posCategory = 'NN'; // Default to noun
    if (posTag.startsWith('JJ')) posCategory = 'JJ';
    else if (posTag.startsWith('RB')) posCategory = 'RB';
    else if (posTag.startsWith('VB')) posCategory = 'VB';

    // Look up synonyms
    const categoryDatabase = synonymDatabase[posCategory] || {};
    return categoryDatabase[word] || [];
}

/**
 * Select best synonym based on frequency and context
 */
function selectBestSynonym(synonyms, originalWord) {
    if (synonyms.length === 0) return originalWord;

    // For now, select randomly from first 3 synonyms (most common)
    const topSynonyms = synonyms.slice(0, Math.min(3, synonyms.length));
    return topSynonyms[Math.floor(Math.random() * topSynonyms.length)];
}

/**
 * Preserve original capitalization pattern
 */
function preserveCapitalization(original, replacement) {
    if (!original || !replacement) return replacement;

    // All uppercase
    if (original === original.toUpperCase()) {
        return replacement.toUpperCase();
    }

    // First letter uppercase
    if (original[0] === original[0].toUpperCase()) {
        return replacement.charAt(0).toUpperCase() + replacement.slice(1).toLowerCase();
    }

    // All lowercase
    return replacement.toLowerCase();
}

/**
 * Get status of available providers
 */
export function getProviderStatus() {
    const status = {};

    Object.entries(MODEL_CONFIGS).forEach(([modelName, config]) => {
        status[modelName] = config.providers.map(provider => ({
            name: provider.name,
            available: !!process.env[provider.apiKeyEnv],
            model: provider.model
        }));
    });

    return status;
}
